import React from 'react';

type Scheme = {
  id: string;
  name: string;
  eligibleCount: number;
  icon: string;
};

type EligibilityListProps = {
  schemes: Scheme[];
};

const EligibilityList: React.FC<EligibilityListProps> = ({ schemes }) => {
  return (
    <div className="space-y-4">
      {schemes.map((scheme) => (
        <div 
          key={scheme.id}
          className="flex items-center p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
        >
          <div className="h-10 w-10 rounded-full bg-indigo-100 dark:bg-indigo-900/50 flex items-center justify-center text-2xl mr-4">
            {scheme.icon}
          </div>
          <div className="flex-1">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white">
              {scheme.name}
            </h4>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {scheme.eligibleCount} eligible citizens
            </p>
          </div>
          <div className="text-right">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
              Eligible
            </span>
          </div>
        </div>
      ))}
    </div>
  );
};

export default EligibilityList;