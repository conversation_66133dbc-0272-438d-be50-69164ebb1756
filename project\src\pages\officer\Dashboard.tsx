import { useState, useEffect } from 'react';
import { Users, BarChart2, Map, Download, Filter } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import StatCard from '../../components/dashboard/StatCard';
import SchemeDistributionChart from '../../components/officer/SchemeDistributionChart';
import DemographicFilter from '../../components/officer/DemographicFilter';
import RegionHeatmap from '../../components/officer/RegionHeatmap';

// Mock data
const mockStats = {
  totalCitizens: 12850,
  eligibleSchemes: 8732,
  coverageRate: 68,
  regionsActive: 24
};

// Mock filter options
const filterOptions = {
  ageGroups: ['0-18', '19-30', '31-45', '46-60', '60+'],
  genders: ['Male', 'Female', 'Other'],
  regions: ['Karnataka', 'Maharashtra', 'Tamil Nadu', 'Gujarat', 'Kerala'],
  castes: ['General', 'OBC', 'SC', 'ST'],
  incomeRanges: ['Below 50k', '50k-1L', '1L-2.5L', '2.5L-5L', 'Above 5L']
};

const OfficerDashboard = () => {
  const { user } = useAuth();
  const { t } = useLanguage();
  const [isLoading, setIsLoading] = useState(true);
  const [activeView, setActiveView] = useState('overview');
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>({});
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  
  useEffect(() => {
    // Simulate loading data
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);
    
    return () => clearTimeout(timer);
  }, []);
  
  // Update active filters
  const handleFilterChange = (filterType: string, values: string[]) => {
    setActiveFilters(prev => ({
      ...prev,
      [filterType]: values
    }));
  };
  
  // Clear all filters
  const clearFilters = () => {
    setActiveFilters({});
  };
  
  // Toggle filter panel visibility
  const toggleFilterPanel = () => {
    setShowFilterPanel(!showFilterPanel);
  };
  
  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    );
  }
  
  // Count active filters
  const activeFilterCount = Object.values(activeFilters).reduce(
    (count, values) => count + values.length, 
    0
  );
  
  return (
    <div className="space-y-6">
      {/* Welcome section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="bg-gradient-to-r from-blue-700 to-indigo-800 px-6 py-4">
          <h2 className="text-xl font-semibold text-white">
            {t('welcome')}, {user?.name}!
          </h2>
          <p className="text-blue-100 mt-1">
            Government Officer Dashboard
          </p>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard 
              title="Total Citizens" 
              value={mockStats.totalCitizens} 
              icon={<Users />} 
              change={+432} 
              period="This month" 
            />
            <StatCard 
              title="Scheme Eligible" 
              value={mockStats.eligibleSchemes} 
              icon={<BarChart2 />} 
              change={+327} 
              period="This month" 
            />
            <StatCard 
              title="Coverage Rate" 
              value={mockStats.coverageRate} 
              icon={<Map />} 
              change={+5} 
              period="vs. last month" 
            />
            <StatCard 
              title="Active Regions" 
              value={mockStats.regionsActive} 
              icon={<Map />} 
              change={+2} 
              period="New this month" 
            />
          </div>
          
          <div className="mt-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="flex space-x-4">
              <button
                onClick={() => setActiveView('overview')}
                className={`px-4 py-2 text-sm font-medium rounded-md ${
                  activeView === 'overview'
                    ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-200'
                    : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white'
                }`}
              >
                Overview
              </button>
              <button
                onClick={() => setActiveView('schemes')}
                className={`px-4 py-2 text-sm font-medium rounded-md ${
                  activeView === 'schemes'
                    ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-200'
                    : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white'
                }`}
              >
                Scheme Distribution
              </button>
              <button
                onClick={() => setActiveView('heatmap')}
                className={`px-4 py-2 text-sm font-medium rounded-md ${
                  activeView === 'heatmap'
                    ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-200'
                    : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white'
                }`}
              >
                Region Heatmap
              </button>
            </div>
            
            <div className="mt-4 sm:mt-0 flex space-x-2">
              <button
                onClick={toggleFilterPanel}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 relative"
              >
                <Filter size={16} className="mr-2" />
                Filters
                {activeFilterCount > 0 && (
                  <span className="absolute -top-2 -right-2 bg-indigo-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {activeFilterCount}
                  </span>
                )}
              </button>
              
              <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <Download size={16} className="mr-2" />
                Export Report
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {/* Filter panel (slide in from right) */}
      <div className={`fixed inset-y-0 right-0 w-80 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out z-20 ${
        showFilterPanel ? 'translate-x-0' : 'translate-x-full'
      }`}>
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Filter Data
          </h3>
          <button 
            onClick={toggleFilterPanel}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            &times;
          </button>
        </div>
        
        <div className="p-4 overflow-y-auto h-full pb-20">
          <DemographicFilter 
            options={filterOptions} 
            activeFilters={activeFilters}
            onFilterChange={handleFilterChange}
          />
          
          <div className="mt-6 flex space-x-2">
            <button
              onClick={clearFilters}
              className="px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Clear All
            </button>
            <button
              onClick={toggleFilterPanel}
              className="px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Apply Filters
            </button>
          </div>
        </div>
      </div>
      
      {/* Backdrop for filter panel */}
      {showFilterPanel && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-25 z-10"
          onClick={toggleFilterPanel}
        ></div>
      )}
      
      {/* Main content based on active view */}
      {activeView === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Citizens by Age Group
            </h3>
            <div className="h-80">
              <SchemeDistributionChart chartType="age" />
            </div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Citizens by Region
            </h3>
            <div className="h-80">
              <SchemeDistributionChart chartType="region" />
            </div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 lg:col-span-2">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Scheme Enrollment Over Time
            </h3>
            <div className="h-80">
              <SchemeDistributionChart chartType="timeline" />
            </div>
          </div>
        </div>
      )}
      
      {activeView === 'schemes' && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Scheme Distribution
          </h3>
          <div className="h-96">
            <SchemeDistributionChart chartType="schemes" />
          </div>
        </div>
      )}
      
      {activeView === 'heatmap' && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Region Heatmap
          </h3>
          <div className="h-96">
            <RegionHeatmap />
          </div>
        </div>
      )}
    </div>
  );
};

export default OfficerDashboard;