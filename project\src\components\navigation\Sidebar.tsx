import { Link, useLocation } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Users, 
  BookOpen, 
  BarChart2, 
  ClipboardList, 
  FileText, 
  Settings, 
  LogOut 
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';

type SidebarProps = {
  role: string;
};

const Sidebar: React.FC<SidebarProps> = ({ role }) => {
  const location = useLocation();
  const { logout, user } = useAuth();
  const { t } = useLanguage();
  
  // Define navigation items based on role
  const getNavItems = () => {
    const basePath = `/${role}`;
    
    const volunteerItems = [
      { 
        path: basePath, 
        label: t('dashboard'), 
        icon: <LayoutDashboard size={20} /> 
      },
      { 
        path: `${basePath}/citizen-form`, 
        label: t('citizens'), 
        icon: <Users size={20} /> 
      },
      { 
        path: `${basePath}/training`, 
        label: t('training'), 
        icon: <BookOpen size={20} /> 
      }
    ];
    
    const supervisorItems = [
      { 
        path: basePath, 
        label: t('dashboard'), 
        icon: <LayoutDashboard size={20} /> 
      },
      { 
        path: `${basePath}/volunteers`, 
        label: t('volunteerPerformance'), 
        icon: <Users size={20} /> 
      },
      { 
        path: `${basePath}/data-quality`, 
        label: t('dataQuality'), 
        icon: <ClipboardList size={20} /> 
      }
    ];
    
    const officerItems = [
      { 
        path: basePath, 
        label: t('dashboard'), 
        icon: <LayoutDashboard size={20} /> 
      },
      { 
        path: `${basePath}/analytics`, 
        label: t('analytics'), 
        icon: <BarChart2 size={20} /> 
      },
      { 
        path: `${basePath}/reports`, 
        label: t('reports'), 
        icon: <FileText size={20} /> 
      },
      { 
        path: `${basePath}/settings`, 
        label: t('settings'), 
        icon: <Settings size={20} /> 
      }
    ];
    
    switch (role) {
      case 'volunteer':
        return volunteerItems;
      case 'supervisor':
        return supervisorItems;
      case 'officer':
        return officerItems;
      default:
        return volunteerItems;
    }
  };
  
  const navItems = getNavItems();
  
  const isActive = (path: string) => {
    return location.pathname === path;
  };
  
  return (
    <div className="h-full flex flex-col">
      {/* User info */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 rounded-full overflow-hidden bg-gray-200">
            {user?.profilePic ? (
              <img 
                src={user.profilePic} 
                alt={user.name} 
                className="h-full w-full object-cover"
              />
            ) : (
              <div className="h-full w-full flex items-center justify-center bg-indigo-600 text-white">
                {user?.name?.charAt(0) || 'U'}
              </div>
            )}
          </div>
          <div>
            <p className="font-medium text-gray-800 dark:text-white">{user?.name}</p>
            <p className="text-sm text-gray-500 dark:text-gray-400 capitalize">{role}</p>
          </div>
        </div>
      </div>
      
      {/* Navigation */}
      <nav className="flex-1 py-4">
        <ul className="space-y-1">
          {navItems.map((item) => (
            <li key={item.path}>
              <Link
                to={item.path}
                className={`flex items-center px-4 py-2 text-sm ${
                  isActive(item.path)
                    ? 'bg-indigo-50 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-200 font-medium'
                    : 'text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
                }`}
              >
                <span className="mr-3">{item.icon}</span>
                {item.label}
              </Link>
            </li>
          ))}
        </ul>
      </nav>
      
      {/* Logout */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={logout}
          className="w-full flex items-center px-4 py-2 text-sm text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700 rounded"
        >
          <LogOut size={20} className="mr-3" />
          {t('logout')}
        </button>
      </div>
    </div>
  );
};

export default Sidebar;