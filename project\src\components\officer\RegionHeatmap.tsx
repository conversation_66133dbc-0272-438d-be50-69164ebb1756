import React, { useEffect, useRef } from 'react';
import { useTheme } from '../../contexts/ThemeContext';

const RegionHeatmap: React.FC = () => {
  const mapRef = useRef<HTMLDivElement>(null);
  const { theme } = useTheme();
  
  useEffect(() => {
    if (mapRef.current) {
      // Similar to the supervisor's map, but with more detailed data
      // In a real app, this would be replaced with a proper mapping library
      const mapContainer = mapRef.current;
      mapContainer.innerHTML = '';
      
      // Create an SVG to represent a simplified India map with heatmap data
      const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
      svg.setAttribute('width', '100%');
      svg.setAttribute('height', '100%');
      svg.setAttribute('viewBox', '0 0 300 300');
      svg.style.backgroundColor = theme === 'dark' ? '#1f2937' : '#f3f4f6';
      
      // Create more detailed regions for heatmap
      const regions = [
        // Karnataka districts
        { id: 'region1', d: 'M50,50 L70,50 L75,65 L60,70 Z', name: 'Bangalore Urban', coverage: 95, schemes: 4 },
        { id: 'region2', d: 'M70,50 L90,50 L85,65 L75,65 Z', name: 'Bangalore Rural', coverage: 82, schemes: 3 },
        { id: 'region3', d: 'M60,70 L75,65 L85,65 L90,80 L70,85 Z', name: 'Mysore', coverage: 88, schemes: 5 },
        { id: 'region4', d: 'M50,50 L60,70 L45,75 L40,60 Z', name: 'Tumkur', coverage: 72, schemes: 3 },
        
        // Maharashtra districts
        { id: 'region5', d: 'M120,40 L140,40 L145,55 L125,60 Z', name: 'Mumbai', coverage: 90, schemes: 4 },
        { id: 'region6', d: 'M140,40 L160,45 L155,60 L145,55 Z', name: 'Pune', coverage: 85, schemes: 4 },
        { id: 'region7', d: 'M125,60 L145,55 L155,60 L150,75 L130,80 Z', name: 'Nagpur', coverage: 65, schemes: 3 },
        { id: 'region8', d: 'M120,40 L125,60 L105,65 L100,50 Z', name: 'Aurangabad', coverage: 55, schemes: 2 },
        
        // Tamil Nadu districts
        { id: 'region9', d: 'M60,120 L80,115 L90,130 L75,140 Z', name: 'Chennai', coverage: 92, schemes: 5 },
        { id: 'region10', d: 'M80,115 L100,110 L105,125 L90,130 Z', name: 'Coimbatore', coverage: 80, schemes: 4 },
        { id: 'region11', d: 'M75,140 L90,130 L105,125 L95,145 Z', name: 'Madurai', coverage: 70, schemes: 3 },
        { id: 'region12', d: 'M60,120 L75,140 L60,150 L50,135 Z', name: 'Salem', coverage: 60, schemes: 2 },
        
        // Gujarat districts
        { id: 'region13', d: 'M30,80 L50,75 L55,90 L40,100 Z', name: 'Ahmedabad', coverage: 88, schemes: 4 },
        { id: 'region14', d: 'M50,75 L70,70 L75,85 L55,90 Z', name: 'Surat', coverage: 75, schemes: 3 },
        { id: 'region15', d: 'M40,100 L55,90 L75,85 L65,105 Z', name: 'Rajkot', coverage: 65, schemes: 3 },
        { id: 'region16', d: 'M30,80 L40,100 L25,110 L20,95 Z', name: 'Vadodara', coverage: 55, schemes: 2 },
        
        // Kerala districts
        { id: 'region17', d: 'M55,150 L75,145 L80,160 L65,170 Z', name: 'Thiruvananthapuram', coverage: 90, schemes: 5 },
        { id: 'region18', d: 'M75,145 L95,145 L100,155 L80,160 Z', name: 'Kochi', coverage: 85, schemes: 4 },
        { id: 'region19', d: 'M65,170 L80,160 L100,155 L90,175 Z', name: 'Kozhikode', coverage: 75, schemes: 3 },
        { id: 'region20', d: 'M55,150 L65,170 L50,180 L45,165 Z', name: 'Kannur', coverage: 65, schemes: 3 }
      ];
      
      // Get color based on coverage and schemes
      const getColor = (coverage: number, schemes: number) => {
        // Intensity based on coverage percentage
        const intensity = Math.min(1.0, Math.max(0.2, coverage / 100));
        
        // Base color variations based on scheme count
        if (schemes >= 5) return `rgba(16, 185, 129, ${intensity})`; // green for high scheme adoption
        if (schemes >= 3) return `rgba(59, 130, 246, ${intensity})`; // blue for medium
        if (schemes >= 2) return `rgba(245, 158, 11, ${intensity})`; // yellow for low
        return `rgba(239, 68, 68, ${intensity})`; // red for very low
      };
      
      // Add regions to SVG with heatmap coloring
      regions.forEach(region => {
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path.setAttribute('d', region.d);
        path.setAttribute('fill', getColor(region.coverage, region.schemes));
        path.setAttribute('stroke', theme === 'dark' ? '#374151' : '#e5e7eb');
        path.setAttribute('stroke-width', '1');
        path.setAttribute('id', region.id);
        
        // Add title for tooltip on hover
        const title = document.createElementNS('http://www.w3.org/2000/svg', 'title');
        title.textContent = `${region.name}: ${region.coverage}% coverage, ${region.schemes} schemes`;
        path.appendChild(title);
        
        // Add hover effect
        path.onmouseover = () => {
          path.setAttribute('fill-opacity', '0.8');
          path.setAttribute('stroke-width', '2');
          
          // Show region info
          const regionInfo = document.getElementById('heatmap-region-info');
          if (regionInfo) {
            regionInfo.textContent = `${region.name}: ${region.coverage}% coverage, ${region.schemes} schemes`;
          }
        };
        
        path.onmouseout = () => {
          path.setAttribute('fill-opacity', '1');
          path.setAttribute('stroke-width', '1');
          
          // Clear region info
          const regionInfo = document.getElementById('heatmap-region-info');
          if (regionInfo) {
            regionInfo.textContent = 'Hover over a region for details';
          }
        };
        
        svg.appendChild(path);
      });
      
      // Add state labels
      const addStateLabel = (x: number, y: number, text: string) => {
        const label = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        label.setAttribute('x', x.toString());
        label.setAttribute('y', y.toString());
        label.setAttribute('fill', theme === 'dark' ? '#e5e7eb' : '#374151');
        label.setAttribute('font-size', '8');
        label.setAttribute('font-weight', 'bold');
        label.textContent = text;
        svg.appendChild(label);
      };
      
      addStateLabel(65, 45, 'Karnataka');
      addStateLabel(135, 35, 'Maharashtra');
      addStateLabel(80, 110, 'Tamil Nadu');
      addStateLabel(45, 70, 'Gujarat');
      addStateLabel(75, 140, 'Kerala');
      
      // Add region info text
      const textElement = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      textElement.setAttribute('x', '10');
      textElement.setAttribute('y', '280');
      textElement.setAttribute('fill', theme === 'dark' ? '#d1d5db' : '#4b5563');
      textElement.setAttribute('font-size', '10');
      textElement.setAttribute('id', 'heatmap-region-info');
      textElement.textContent = 'Hover over a region for details';
      svg.appendChild(textElement);
      
      // Add legend
      const legendY = 240;
      const addLegendItem = (x: number, color: string, text: string) => {
        const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        rect.setAttribute('x', x.toString());
        rect.setAttribute('y', legendY.toString());
        rect.setAttribute('width', '10');
        rect.setAttribute('height', '10');
        rect.setAttribute('fill', color);
        
        const label = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        label.setAttribute('x', (x + 15).toString());
        label.setAttribute('y', (legendY + 8).toString());
        label.setAttribute('fill', theme === 'dark' ? '#d1d5db' : '#4b5563');
        label.setAttribute('font-size', '8');
        label.textContent = text;
        
        svg.appendChild(rect);
        svg.appendChild(label);
      };
      
      addLegendItem(10, 'rgba(16, 185, 129, 0.8)', '5+ Schemes');
      addLegendItem(85, 'rgba(59, 130, 246, 0.8)', '3-4 Schemes');
      addLegendItem(165, 'rgba(245, 158, 11, 0.8)', '2 Schemes');
      addLegendItem(230, 'rgba(239, 68, 68, 0.8)', '0-1 Schemes');
      
      mapContainer.appendChild(svg);
    }
  }, [theme]);
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <select className="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm">
            <option>Scheme Adoption</option>
            <option>Data Coverage</option>
            <option>Volunteer Activity</option>
          </select>
        </div>
        <div className="flex space-x-2">
          <button className="px-2 py-1 text-xs border border-gray-300 rounded shadow-sm text-gray-700 bg-white hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 focus:outline-none">
            State View
          </button>
          <button className="px-2 py-1 text-xs border border-gray-300 rounded shadow-sm text-gray-700 bg-white hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 focus:outline-none">
            District View
          </button>
          <button className="px-2 py-1 text-xs border border-transparent rounded shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none">
            Block View
          </button>
        </div>
      </div>
      
      <div ref={mapRef} className="w-full h-full border border-gray-200 dark:border-gray-700 rounded"></div>
      
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mt-4">
        <div className="bg-white dark:bg-gray-700 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
          <div className="text-xs text-gray-500 dark:text-gray-400">Top Performing District</div>
          <div className="text-lg font-medium text-gray-900 dark:text-white">Bangalore Urban</div>
          <div className="text-sm text-green-600 dark:text-green-400">95% coverage</div>
        </div>
        
        <div className="bg-white dark:bg-gray-700 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
          <div className="text-xs text-gray-500 dark:text-gray-400">Critical Coverage District</div>
          <div className="text-lg font-medium text-gray-900 dark:text-white">Aurangabad</div>
          <div className="text-sm text-red-600 dark:text-red-400">55% coverage</div>
        </div>
        
        <div className="bg-white dark:bg-gray-700 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
          <div className="text-xs text-gray-500 dark:text-gray-400">Highest Scheme Adoption</div>
          <div className="text-lg font-medium text-gray-900 dark:text-white">Thiruvananthapuram</div>
          <div className="text-sm text-indigo-600 dark:text-indigo-400">5 schemes</div>
        </div>
        
        <div className="bg-white dark:bg-gray-700 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
          <div className="text-xs text-gray-500 dark:text-gray-400">Most Improved</div>
          <div className="text-lg font-medium text-gray-900 dark:text-white">Coimbatore</div>
          <div className="text-sm text-green-600 dark:text-green-400">+15% this month</div>
        </div>
      </div>
    </div>
  );
};

export default RegionHeatmap;