import { useState, useEffect } from 'react';
import { UserChe<PERSON>, AlertTriangle, Award, Map, Users } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import StatCard from '../../components/dashboard/StatCard';
import VolunteerTable from '../../components/supervisor/VolunteerTable';
import RegionMap from '../../components/supervisor/RegionMap';
import QualityChart from '../../components/supervisor/QualityChart';

// Mock data
const mockStats = {
  totalVolunteers: 24,
  activeTodayVolunteers: 18,
  pendingReviews: 37,
  completedReviews: 283,
  citizensProcessed: 320
};

const SupervisorDashboard = () => {
  const { user } = useAuth();
  const { t } = useLanguage();
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  
  useEffect(() => {
    // Simulate loading data
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);
    
    return () => clearTimeout(timer);
  }, []);
  
  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      {/* Welcome section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="bg-gradient-to-r from-green-600 to-teal-500 px-6 py-4">
          <h2 className="text-xl font-semibold text-white">
            {t('welcome')}, {user?.name}!
          </h2>
          <p className="text-green-100 mt-1">
            Supervisor - {user?.region} Region
          </p>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <StatCard 
              title="Volunteers" 
              value={mockStats.totalVolunteers} 
              icon={<Users />} 
              change={+3} 
              period="This month" 
            />
            <StatCard 
              title="Active Today" 
              value={mockStats.activeTodayVolunteers} 
              icon={<UserCheck />} 
              change={+5} 
              period="vs. yesterday" 
            />
            <StatCard 
              title="Pending Reviews" 
              value={mockStats.pendingReviews} 
              icon={<AlertTriangle />} 
              change={-8} 
              period="vs. yesterday" 
            />
            <StatCard 
              title="Citizens Processed" 
              value={mockStats.citizensProcessed} 
              icon={<Award />} 
              change={+12} 
              period="This week" 
            />
          </div>
        </div>
      </div>
      
      {/* Tabs navigation */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <nav className="flex border-b border-gray-200 dark:border-gray-700">
          <button
            onClick={() => setActiveTab('overview')}
            className={`px-4 py-3 text-sm font-medium ${
              activeTab === 'overview'
                ? 'border-b-2 border-indigo-500 text-indigo-600 dark:text-indigo-400'
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            Overview
          </button>
          <button
            onClick={() => setActiveTab('volunteers')}
            className={`px-4 py-3 text-sm font-medium ${
              activeTab === 'volunteers'
                ? 'border-b-2 border-indigo-500 text-indigo-600 dark:text-indigo-400'
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            Volunteers
          </button>
          <button
            onClick={() => setActiveTab('map')}
            className={`px-4 py-3 text-sm font-medium ${
              activeTab === 'map'
                ? 'border-b-2 border-indigo-500 text-indigo-600 dark:text-indigo-400'
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            Region Map
          </button>
        </nav>
        
        {/* Tab content */}
        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Data Quality Overview
              </h3>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-4">
                    Quality Score Trends
                  </h4>
                  <div className="h-60">
                    <QualityChart />
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-4">
                    Key Metrics
                  </h4>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm text-gray-700 dark:text-gray-300">Data Completeness</span>
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">87%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                        <div className="bg-green-500 h-2.5 rounded-full" style={{ width: '87%' }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm text-gray-700 dark:text-gray-300">Verification Rate</span>
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">72%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                        <div className="bg-blue-500 h-2.5 rounded-full" style={{ width: '72%' }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm text-gray-700 dark:text-gray-300">Error Rate</span>
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">5%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                        <div className="bg-red-500 h-2.5 rounded-full" style={{ width: '5%' }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm text-gray-700 dark:text-gray-300">Scheme Eligibility Match</span>
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">93%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                        <div className="bg-purple-500 h-2.5 rounded-full" style={{ width: '93%' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mt-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Recent Activity
                </h3>
                <div className="space-y-4">
                  {[1, 2, 3].map((item) => (
                    <div 
                      key={item}
                      className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 flex items-start"
                    >
                      <div className="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 dark:bg-indigo-900/50 flex items-center justify-center text-indigo-600 dark:text-indigo-400 mr-4">
                        <UserCheck size={20} />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          Volunteer Priya S. added 8 new citizen profiles
                        </h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {item === 1 ? '2 hours ago' : item === 2 ? 'Yesterday' : '2 days ago'}
                        </p>
                      </div>
                      <div>
                        <button className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300 text-sm font-medium">
                          Review
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
          
          {activeTab === 'volunteers' && (
            <div>
              <div className="sm:flex sm:items-center sm:justify-between mb-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Volunteer Performance
                </h3>
                <div className="mt-3 sm:mt-0 flex space-x-2">
                  <select className="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm">
                    <option>All Regions</option>
                    <option>Karnataka</option>
                    <option>Maharashtra</option>
                    <option>Tamil Nadu</option>
                  </select>
                  <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Export
                  </button>
                </div>
              </div>
              <VolunteerTable />
            </div>
          )}
          
          {activeTab === 'map' && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Region Coverage Map
              </h3>
              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                <div className="h-80">
                  <RegionMap />
                </div>
              </div>
              <div className="mt-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-white dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                    <span className="text-sm text-gray-700 dark:text-gray-300">High Coverage (90%+)</span>
                  </div>
                </div>
                <div className="bg-white dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                    <span className="text-sm text-gray-700 dark:text-gray-300">Medium Coverage (50-90%)</span>
                  </div>
                </div>
                <div className="bg-white dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                    <span className="text-sm text-gray-700 dark:text-gray-300">Low Coverage (20-50%)</span>
                  </div>
                </div>
                <div className="bg-white dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                    <span className="text-sm text-gray-700 dark:text-gray-300">Critical Coverage (&lt;20%)</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SupervisorDashboard;