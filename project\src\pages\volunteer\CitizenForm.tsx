import { useState } from 'react';
import { Upload, Save, CheckCircle } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useLanguage } from '../../contexts/LanguageContext';
import { useAuth } from '../../contexts/AuthContext';

// Form field type
type FormField = {
  id: string;
  name: string;
  label: TranslationKey;
  type: 'text' | 'number' | 'select' | 'radio' | 'textarea';
  options?: { value: string; label: TranslationKey }[];
  required: boolean;
  pattern?: string;
  min?: number;
  max?: number;
  placeholder?: string;
};

// Initial form data
const initialFormData = {
  name: '',
  age: '',
  gender: '',
  region: '',
  caste: '',
  income: '',
  education: '',
  healthStatus: '',
  occupation: '',
  familySize: '',
  address: '',
  mobileNumber: '',
  hasAadhaar: 'no',
  aadhaarNumber: '',
  remarks: ''
};

// Form fields configuration
const formFields: FormField[] = [
  {
    id: 'name',
    name: 'name',
    label: 'name',
    type: 'text',
    required: true,
    placeholder: 'Enter full name'
  },
  {
    id: 'age',
    name: 'age',
    label: 'age',
    type: 'number',
    required: true,
    min: 0,
    max: 120
  },
  {
    id: 'gender',
    name: 'gender',
    label: 'gender',
    type: 'radio',
    options: [
      { value: 'male', label: 'male' },
      { value: 'female', label: 'female' },
      { value: 'other', label: 'other' }
    ],
    required: true
  },
  {
    id: 'region',
    name: 'region',
    label: 'region',
    type: 'select',
    options: [
      { value: '', label: 'selectLanguage' },
      { value: 'Karnataka', label: 'region' },
      { value: 'Maharashtra', label: 'region' },
      { value: 'Tamil Nadu', label: 'region' },
      { value: 'Gujarat', label: 'region' },
      { value: 'Kerala', label: 'region' }
    ],
    required: true
  },
  {
    id: 'caste',
    name: 'caste',
    label: 'caste',
    type: 'select',
    options: [
      { value: '', label: 'selectLanguage' },
      { value: 'General', label: 'caste' },
      { value: 'OBC', label: 'caste' },
      { value: 'SC', label: 'caste' },
      { value: 'ST', label: 'caste' }
    ],
    required: true
  },
  {
    id: 'income',
    name: 'income',
    label: 'income',
    type: 'select',
    options: [
      { value: '', label: 'selectLanguage' },
      { value: 'Below 50,000', label: 'income' },
      { value: '50,000 - 1,00,000', label: 'income' },
      { value: '1,00,000 - 2,50,000', label: 'income' },
      { value: '2,50,000 - 5,00,000', label: 'income' },
      { value: 'Above 5,00,000', label: 'income' }
    ],
    required: true
  },
  {
    id: 'education',
    name: 'education',
    label: 'education',
    type: 'select',
    options: [
      { value: '', label: 'selectLanguage' },
      { value: 'No formal education', label: 'education' },
      { value: 'Primary education', label: 'education' },
      { value: 'Secondary education', label: 'education' },
      { value: 'Higher secondary', label: 'education' },
      { value: 'Graduate', label: 'education' },
      { value: 'Post-graduate', label: 'education' }
    ],
    required: true
  },
  {
    id: 'healthStatus',
    name: 'healthStatus',
    label: 'health',
    type: 'select',
    options: [
      { value: '', label: 'selectLanguage' },
      { value: 'Good', label: 'health' },
      { value: 'Fair', label: 'health' },
      { value: 'Poor', label: 'health' },
      { value: 'Critical', label: 'health' }
    ],
    required: true
  },
  {
    id: 'occupation',
    name: 'occupation',
    label: 'occupation',
    type: 'text',
    required: false
  },
  {
    id: 'familySize',
    name: 'familySize',
    label: 'familySize',
    type: 'number',
    required: false,
    min: 1,
    max: 30
  },
  {
    id: 'address',
    name: 'address',
    label: 'address',
    type: 'textarea',
    required: false
  },
  {
    id: 'mobileNumber',
    name: 'mobileNumber',
    label: 'mobileNumber',
    type: 'text',
    pattern: '^[0-9]{10}$',
    required: false,
    placeholder: '10-digit mobile number'
  },
  {
    id: 'hasAadhaar',
    name: 'hasAadhaar',
    label: 'hasAadhaar',
    type: 'radio',
    options: [
      { value: 'yes', label: 'yes' },
      { value: 'no', label: 'no' }
    ],
    required: true
  },
  {
    id: 'aadhaarNumber',
    name: 'aadhaarNumber',
    label: 'aadhaarNumber',
    type: 'text',
    pattern: '^[0-9]{12}$',
    required: false,
    placeholder: '12-digit Aadhaar number'
  },
  {
    id: 'remarks',
    name: 'remarks',
    label: 'remarks',
    type: 'textarea',
    required: false
  }
];

const CitizenForm = () => {
  const { t } = useLanguage();
  const { user } = useAuth();
  const [formData, setFormData] = useState(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isSuccess, setIsSuccess] = useState(false);
  
  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // If hasAadhaar is changed to "no", clear aadhaarNumber
    if (name === 'hasAadhaar' && value === 'no') {
      setFormData(prev => ({ ...prev, [name]: value, aadhaarNumber: '' }));
    }
  };
  
  // Handle document upload
  const handleDocumentUpload = () => {
    setIsUploading(true);
    
    // Simulate progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      setUploadProgress(progress);
      
      if (progress >= 100) {
        clearInterval(interval);
        setTimeout(() => {
          setIsUploading(false);
          setUploadProgress(0);
          
          // Simulate OCR data extraction
          setFormData(prev => ({
            ...prev,
            name: 'Amit Kumar Singh',
            age: '32',
            gender: 'male',
            region: 'Karnataka',
            hasAadhaar: 'yes',
            aadhaarNumber: '123456789012'
          }));
          
          toast.success('Document processed successfully! Form fields updated.');
        }, 500);
      }
    }, 300);
  };
  
  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSuccess(true);
      toast.success('Citizen data saved successfully!');
      
      // Reset form after showing success message
      setTimeout(() => {
        setFormData(initialFormData);
        setIsSuccess(false);
      }, 2000);
    }, 1500);
  };
  
  // Group fields into sections for better organization
  const personalInfoFields = formFields.filter(field => 
    ['name', 'age', 'gender', 'address', 'mobileNumber'].includes(field.id)
  );
  
  const socioeconomicFields = formFields.filter(field => 
    ['region', 'caste', 'income', 'education', 'occupation', 'familySize'].includes(field.id)
  );
  
  const healthFields = formFields.filter(field => 
    ['healthStatus'].includes(field.id)
  );
  
  const identificationFields = formFields.filter(field => 
    ['hasAadhaar', 'aadhaarNumber', 'remarks'].includes(field.id)
  );
  
  // Render form field based on type
  const renderField = (field: FormField) => {
    switch (field.type) {
      case 'text':
      case 'number':
        return (
          <input
            type={field.type}
            id={field.id}
            name={field.name}
            value={formData[field.name as keyof typeof formData] as string}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
            required={field.required}
            min={field.min}
            max={field.max}
            pattern={field.pattern}
            placeholder={field.placeholder}
          />
        );
        
      case 'select':
        return (
          <select
            id={field.id}
            name={field.name}
            value={formData[field.name as keyof typeof formData] as string}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
            required={field.required}
          >
            {field.options?.map((option) => (
              <option key={option.value} value={option.value}>
                {option.value || t(option.label)}
              </option>
            ))}
          </select>
        );
        
      case 'radio':
        return (
          <div className="mt-1 space-y-2 sm:space-y-0 sm:space-x-6 sm:flex">
            {field.options?.map((option) => (
              <div key={option.value} className="flex items-center">
                <input
                  type="radio"
                  id={`${field.id}-${option.value}`}
                  name={field.name}
                  value={option.value}
                  checked={formData[field.name as keyof typeof formData] === option.value}
                  onChange={handleChange}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-600"
                  required={field.required}
                />
                <label
                  htmlFor={`${field.id}-${option.value}`}
                  className="ml-2 block text-sm text-gray-700 dark:text-gray-300"
                >
                  {t(option.label)}
                </label>
              </div>
            ))}
          </div>
        );
        
      case 'textarea':
        return (
          <textarea
            id={field.id}
            name={field.name}
            value={formData[field.name as keyof typeof formData] as string}
            onChange={handleChange}
            rows={3}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
            required={field.required}
          />
        );
        
      default:
        return null;
    }
  };
  
  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div className="bg-gradient-to-r from-indigo-600 to-blue-500 px-6 py-4">
          <h2 className="text-xl font-semibold text-white">
            {t('addCitizen')}
          </h2>
          <p className="text-indigo-100 mt-1">
            Fill in the details below or upload documents for auto-filling
          </p>
        </div>
        
        {/* Document upload section */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                {t('uploadDocuments')}
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Upload Aadhaar or other government documents to auto-fill the form
              </p>
            </div>
            <div className="mt-4 sm:mt-0">
              <button
                type="button"
                onClick={handleDocumentUpload}
                disabled={isUploading}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                <Upload size={16} className="mr-2" />
                {isUploading ? 'Uploading...' : 'Upload Document'}
              </button>
            </div>
          </div>
          
          {/* Upload progress */}
          {isUploading && (
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                <div 
                  className="bg-indigo-600 h-2.5 rounded-full transition-all duration-300" 
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 text-right">
                {uploadProgress}% complete
              </p>
            </div>
          )}
        </div>
        
        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-8">
          {/* Personal Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Personal Information
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-y-6 gap-x-4">
              {personalInfoFields.map((field) => (
                <div key={field.id}>
                  <label 
                    htmlFor={field.id} 
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    {t(field.label)}{field.required && <span className="text-red-500 ml-1">*</span>}
                  </label>
                  {renderField(field)}
                </div>
              ))}
            </div>
          </div>
          
          {/* Socioeconomic Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Socioeconomic Information
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-y-6 gap-x-4">
              {socioeconomicFields.map((field) => (
                <div key={field.id}>
                  <label 
                    htmlFor={field.id} 
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    {t(field.label)}{field.required && <span className="text-red-500 ml-1">*</span>}
                  </label>
                  {renderField(field)}
                </div>
              ))}
            </div>
          </div>
          
          {/* Health Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Health Information
            </h3>
            <div className="grid grid-cols-1 gap-y-6 gap-x-4">
              {healthFields.map((field) => (
                <div key={field.id}>
                  <label 
                    htmlFor={field.id} 
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    {t(field.label)}{field.required && <span className="text-red-500 ml-1">*</span>}
                  </label>
                  {renderField(field)}
                </div>
              ))}
            </div>
          </div>
          
          {/* Identification */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Identification
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-y-6 gap-x-4">
              {identificationFields.map((field) => (
                <div key={field.id} className={field.id === 'remarks' ? 'sm:col-span-2' : ''}>
                  <label 
                    htmlFor={field.id} 
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    {t(field.label)}{field.required && <span className="text-red-500 ml-1">*</span>}
                  </label>
                  {renderField(field)}
                  
                  {/* Only show Aadhaar field if hasAadhaar is yes */}
                  {field.id === 'aadhaarNumber' && formData.hasAadhaar !== 'yes' && (
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                      Select "Yes" in "Has Aadhaar" to enter Aadhaar number
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>
          
          {/* Submit Buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              className="px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              {t('cancel')}
            </button>
            <button
              type="submit"
              disabled={isSubmitting || isSuccess}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 transition-colors"
            >
              {isSubmitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </>
              ) : isSuccess ? (
                <>
                  <CheckCircle size={16} className="mr-2" />
                  Saved!
                </>
              ) : (
                <>
                  <Save size={16} className="mr-2" />
                  {t('submit')}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CitizenForm;