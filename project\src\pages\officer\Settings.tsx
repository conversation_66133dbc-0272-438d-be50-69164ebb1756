import React, { useState } from 'react';
import { Save, Bell, Lock, Globe, Moon, Sun } from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';

const Settings = () => {
  const { theme, toggleTheme } = useTheme();
  const { language, setLanguage } = useLanguage();
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    smsNotifications: false,
    reportDigest: 'weekly'
  });
  
  const handleNotificationChange = (setting: keyof typeof notificationSettings) => {
    if (typeof notificationSettings[setting] === 'boolean') {
      setNotificationSettings(prev => ({
        ...prev,
        [setting]: !prev[setting]
      }));
    }
  };
  
  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Settings
          </h2>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Manage your application preferences and notifications
          </p>
        </div>
        
        <div className="border-t border-gray-200 dark:border-gray-700">
          <dl className="divide-y divide-gray-200 dark:divide-gray-700">
            {/* Appearance */}
            <div className="px-6 py-4">
              <dt className="text-sm font-medium text-gray-900 dark:text-white">
                Appearance
              </dt>
              <dd className="mt-1">
                <button
                  onClick={toggleTheme}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  {theme === 'dark' ? (
                    <>
                      <Sun size={16} className="mr-2" />
                      Light Mode
                    </>
                  ) : (
                    <>
                      <Moon size={16} className="mr-2" />
                      Dark Mode
                    </>
                  )}
                </button>
              </dd>
            </div>
            
            {/* Language */}
            <div className="px-6 py-4">
              <dt className="text-sm font-medium text-gray-900 dark:text-white">
                Language
              </dt>
              <dd className="mt-1">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => setLanguage('en')}
                    className={`inline-flex items-center px-3 py-2 border shadow-sm text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
                      language === 'en'
                        ? 'border-indigo-500 text-indigo-700 bg-indigo-50 dark:bg-indigo-900/30 dark:text-indigo-200'
                        : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600'
                    }`}
                  >
                    <Globe size={16} className="mr-2" />
                    English
                  </button>
                  <button
                    onClick={() => setLanguage('hi')}
                    className={`inline-flex items-center px-3 py-2 border shadow-sm text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
                      language === 'hi'
                        ? 'border-indigo-500 text-indigo-700 bg-indigo-50 dark:bg-indigo-900/30 dark:text-indigo-200'
                        : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600'
                    }`}
                  >
                    <Globe size={16} className="mr-2" />
                    हिंदी
                  </button>
                </div>
              </dd>
            </div>
            
            {/* Notifications */}
            <div className="px-6 py-4">
              <dt className="text-sm font-medium text-gray-900 dark:text-white">
                Notifications
              </dt>
              <dd className="mt-2 space-y-3">
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="email-notifications"
                      type="checkbox"
                      checked={notificationSettings.emailNotifications}
                      onChange={() => handleNotificationChange('emailNotifications')}
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded dark:border-gray-600"
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="email-notifications" className="font-medium text-gray-700 dark:text-gray-200">
                      Email Notifications
                    </label>
                    <p className="text-gray-500 dark:text-gray-400">
                      Receive email updates about citizen data changes
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="sms-notifications"
                      type="checkbox"
                      checked={notificationSettings.smsNotifications}
                      onChange={() => handleNotificationChange('smsNotifications')}
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded dark:border-gray-600"
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="sms-notifications" className="font-medium text-gray-700 dark:text-gray-200">
                      SMS Notifications
                    </label>
                    <p className="text-gray-500 dark:text-gray-400">
                      Receive SMS alerts for important updates
                    </p>
                  </div>
                </div>
                
                <div className="sm:flex sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                  <label htmlFor="report-digest" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                    Report Digest Frequency
                  </label>
                  <select
                    id="report-digest"
                    value={notificationSettings.reportDigest}
                    onChange={(e) => setNotificationSettings(prev => ({
                      ...prev,
                      reportDigest: e.target.value
                    }))}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  >
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                </div>
              </dd>
            </div>
            
            {/* Security */}
            <div className="px-6 py-4">
              <dt className="text-sm font-medium text-gray-900 dark:text-white">
                Security
              </dt>
              <dd className="mt-1">
                <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                  <Lock size={16} className="mr-2" />
                  Change Password
                </button>
              </dd>
            </div>
          </dl>
        </div>
        
        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex justify-end">
            <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              <Save size={16} className="mr-2" />
              Save Changes
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;