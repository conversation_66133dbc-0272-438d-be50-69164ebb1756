import React, { useState } from 'react';

type DemographicFilterProps = {
  options: {
    ageGroups: string[];
    genders: string[];
    regions: string[];
    castes: string[];
    incomeRanges: string[];
  };
  activeFilters: Record<string, string[]>;
  onFilterChange: (filterType: string, values: string[]) => void;
};

const DemographicFilter: React.FC<DemographicFilterProps> = ({
  options,
  activeFilters,
  onFilterChange
}) => {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    age: true,
    gender: true,
    region: true,
    caste: false,
    income: false
  });
  
  // Toggle section expansion
  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };
  
  // Handle checkbox change
  const handleCheckboxChange = (filterType: string, value: string) => {
    const currentValues = activeFilters[filterType] || [];
    let newValues: string[];
    
    if (currentValues.includes(value)) {
      newValues = currentValues.filter(v => v !== value);
    } else {
      newValues = [...currentValues, value];
    }
    
    onFilterChange(filterType, newValues);
  };
  
  return (
    <div className="space-y-4">
      {/* Age Groups */}
      <div className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
        <button
          onClick={() => toggleSection('age')}
          className="w-full flex justify-between items-center p-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none"
        >
          <span>Age Groups</span>
          <span>{expandedSections.age ? '−' : '+'}</span>
        </button>
        
        {expandedSections.age && (
          <div className="p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
            <div className="space-y-2">
              {options.ageGroups.map(age => (
                <label key={age} className="flex items-center">
                  <input
                    type="checkbox"
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded dark:border-gray-600"
                    checked={(activeFilters.age || []).includes(age)}
                    onChange={() => handleCheckboxChange('age', age)}
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">{age}</span>
                </label>
              ))}
            </div>
          </div>
        )}
      </div>
      
      {/* Gender */}
      <div className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
        <button
          onClick={() => toggleSection('gender')}
          className="w-full flex justify-between items-center p-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none"
        >
          <span>Gender</span>
          <span>{expandedSections.gender ? '−' : '+'}</span>
        </button>
        
        {expandedSections.gender && (
          <div className="p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
            <div className="space-y-2">
              {options.genders.map(gender => (
                <label key={gender} className="flex items-center">
                  <input
                    type="checkbox"
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded dark:border-gray-600"
                    checked={(activeFilters.gender || []).includes(gender)}
                    onChange={() => handleCheckboxChange('gender', gender)}
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">{gender}</span>
                </label>
              ))}
            </div>
          </div>
        )}
      </div>
      
      {/* Region */}
      <div className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
        <button
          onClick={() => toggleSection('region')}
          className="w-full flex justify-between items-center p-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none"
        >
          <span>Region</span>
          <span>{expandedSections.region ? '−' : '+'}</span>
        </button>
        
        {expandedSections.region && (
          <div className="p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
            <div className="space-y-2">
              {options.regions.map(region => (
                <label key={region} className="flex items-center">
                  <input
                    type="checkbox"
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded dark:border-gray-600"
                    checked={(activeFilters.region || []).includes(region)}
                    onChange={() => handleCheckboxChange('region', region)}
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">{region}</span>
                </label>
              ))}
            </div>
          </div>
        )}
      </div>
      
      {/* Caste */}
      <div className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
        <button
          onClick={() => toggleSection('caste')}
          className="w-full flex justify-between items-center p-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none"
        >
          <span>Caste</span>
          <span>{expandedSections.caste ? '−' : '+'}</span>
        </button>
        
        {expandedSections.caste && (
          <div className="p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
            <div className="space-y-2">
              {options.castes.map(caste => (
                <label key={caste} className="flex items-center">
                  <input
                    type="checkbox"
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded dark:border-gray-600"
                    checked={(activeFilters.caste || []).includes(caste)}
                    onChange={() => handleCheckboxChange('caste', caste)}
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">{caste}</span>
                </label>
              ))}
            </div>
          </div>
        )}
      </div>
      
      {/* Income Range */}
      <div className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
        <button
          onClick={() => toggleSection('income')}
          className="w-full flex justify-between items-center p-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none"
        >
          <span>Income Range</span>
          <span>{expandedSections.income ? '−' : '+'}</span>
        </button>
        
        {expandedSections.income && (
          <div className="p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
            <div className="space-y-2">
              {options.incomeRanges.map(income => (
                <label key={income} className="flex items-center">
                  <input
                    type="checkbox"
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded dark:border-gray-600"
                    checked={(activeFilters.income || []).includes(income)}
                    onChange={() => handleCheckboxChange('income', income)}
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">{income}</span>
                </label>
              ))}
            </div>
          </div>
        )}
      </div>
      
      {/* Natural language query */}
      <div className="mt-6">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Natural Language Query
        </label>
        <div className="relative">
          <input
            type="text"
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
            placeholder="e.g., show low-income women in Karnataka"
          />
          <button className="absolute inset-y-0 right-0 pr-3 flex items-center text-indigo-600 dark:text-indigo-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
          Try queries like "elderly in rural areas" or "SC/ST beneficiaries"
        </p>
      </div>
    </div>
  );
};

export default DemographicFilter;