import { useState } from 'react';
import { Play, Pause, FileText, CheckCircle, Award } from 'lucide-react';

// Training video data
const trainingVideos = [
  {
    id: '1',
    title: 'Introduction to CitizenConnect',
    duration: '5:32',
    thumbnail: 'https://images.pexels.com/photos/7688336/pexels-photo-7688336.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    description: 'Learn about the CitizenConnect platform and how it helps citizens access government schemes.',
    watched: true
  },
  {
    id: '2',
    title: 'Data Collection Best Practices',
    duration: '8:15',
    thumbnail: 'https://images.pexels.com/photos/8326305/pexels-photo-8326305.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    description: 'Understand how to collect accurate and complete data from citizens to maximize their benefits.',
    watched: false
  },
  {
    id: '3',
    title: 'Document Verification Process',
    duration: '6:48',
    thumbnail: 'https://images.pexels.com/photos/7681266/pexels-photo-7681266.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    description: 'Learn how to verify various government documents and use OCR to extract information.',
    watched: false
  },
  {
    id: '4',
    title: 'Explaining Scheme Eligibility',
    duration: '7:22',
    thumbnail: 'https://images.pexels.com/photos/8297452/pexels-photo-8297452.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    description: 'Understand different government schemes and how to explain eligibility criteria to citizens.',
    watched: false
  }
];

// Quiz questions
const quizQuestions = [
  {
    id: '1',
    question: 'What is the primary purpose of CitizenConnect?',
    options: [
      'To provide entertainment content',
      'To enhance citizen data for better scheme eligibility',
      'To collect taxes from citizens',
      'To conduct government surveys'
    ],
    correctAnswer: 1
  },
  {
    id: '2',
    question: 'Which document is most commonly used for identity verification?',
    options: [
      'Birth certificate',
      'School leaving certificate',
      'Aadhaar card',
      'Driving license'
    ],
    correctAnswer: 2
  },
  {
    id: '3',
    question: 'What should you do if a citizen does not have any identity documents?',
    options: [
      'Refuse to register them',
      'Create a fake document',
      'Register with available information and mark as "pending verification"',
      'Ask them to come back later'
    ],
    correctAnswer: 2
  },
  {
    id: '4',
    question: 'How can you verify if the data you collected is accurate?',
    options: [
      'Ask the citizen to confirm verbally',
      'Cross-check with multiple documents',
      'Just assume it\'s correct',
      'Only accept data from digital sources'
    ],
    correctAnswer: 1
  },
  {
    id: '5',
    question: 'What is the recommended approach when explaining scheme benefits to citizens?',
    options: [
      'Promise them all benefits regardless of eligibility',
      'Avoid mentioning any schemes to prevent disappointment',
      'Clearly explain eligibility criteria and potential benefits',
      'Only mention schemes that provide monetary benefits'
    ],
    correctAnswer: 2
  }
];

const VolunteerTraining = () => {
  const [selectedVideo, setSelectedVideo] = useState(trainingVideos[0]);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showQuiz, setShowQuiz] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState<number[]>([]);
  const [quizSubmitted, setQuizSubmitted] = useState(false);
  const [score, setScore] = useState(0);
  
  // Handle video selection
  const handleVideoSelect = (video: typeof trainingVideos[0]) => {
    setSelectedVideo(video);
    setIsPlaying(false);
    setShowQuiz(false);
  };
  
  // Toggle play/pause
  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };
  
  // Start quiz
  const handleStartQuiz = () => {
    setShowQuiz(true);
    setCurrentQuestion(0);
    setSelectedAnswers([]);
    setQuizSubmitted(false);
  };
  
  // Handle answer selection
  const handleAnswerSelect = (answerIndex: number) => {
    if (quizSubmitted) return;
    
    const newAnswers = [...selectedAnswers];
    newAnswers[currentQuestion] = answerIndex;
    setSelectedAnswers(newAnswers);
  };
  
  // Navigate to next question
  const handleNextQuestion = () => {
    if (currentQuestion < quizQuestions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    }
  };
  
  // Navigate to previous question
  const handlePrevQuestion = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1);
    }
  };
  
  // Submit quiz
  const handleSubmitQuiz = () => {
    // Calculate score
    let correctCount = 0;
    selectedAnswers.forEach((answer, index) => {
      if (answer === quizQuestions[index].correctAnswer) {
        correctCount++;
      }
    });
    
    setScore(correctCount);
    setQuizSubmitted(true);
  };
  
  // Calculate progress
  const watchedCount = trainingVideos.filter(video => video.watched).length;
  const progressPercentage = (watchedCount / trainingVideos.length) * 100;
  
  return (
    <div className="space-y-6">
      {/* Training progress */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Training Progress
        </h2>
        <div className="flex items-center">
          <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mr-4">
            <div 
              className="bg-indigo-600 h-2.5 rounded-full" 
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap">
            {watchedCount} / {trainingVideos.length} complete
          </span>
        </div>
        
        {progressPercentage === 100 && (
          <div className="mt-4 flex items-center text-green-600 dark:text-green-400">
            <Award size={20} className="mr-2" />
            <span className="font-medium">All training completed! You've earned the Training Expert badge.</span>
          </div>
        )}
      </div>
      
      {/* Video player and list */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Video player */}
        <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          {!showQuiz ? (
            <>
              {/* Video preview */}
              <div className="relative aspect-video bg-gray-900">
                <img 
                  src={selectedVideo.thumbnail} 
                  alt={selectedVideo.title}
                  className="w-full h-full object-cover opacity-80"
                />
                <div className="absolute inset-0 flex items-center justify-center">
                  <button
                    onClick={togglePlayPause}
                    className="p-4 bg-indigo-600 rounded-full text-white hover:bg-indigo-700 transition-colors"
                  >
                    {isPlaying ? <Pause size={24} /> : <Play size={24} />}
                  </button>
                </div>
                {isPlaying && (
                  <div className="absolute bottom-4 left-4 right-4 bg-gray-900/60 text-white px-3 py-1 rounded">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">1:45 / {selectedVideo.duration}</span>
                      <div className="flex-1 mx-3">
                        <div className="h-1 bg-gray-600 rounded-full">
                          <div className="h-1 bg-indigo-500 rounded-full w-1/3"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Video info */}
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                  {selectedVideo.title}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  Duration: {selectedVideo.duration}
                </p>
                <p className="mt-4 text-gray-700 dark:text-gray-300">
                  {selectedVideo.description}
                </p>
                <div className="mt-6 flex space-x-3">
                  <button
                    onClick={togglePlayPause}
                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    {isPlaying ? <Pause size={16} className="mr-2" /> : <Play size={16} className="mr-2" />}
                    {isPlaying ? 'Pause' : 'Play'} Video
                  </button>
                  <button
                    onClick={handleStartQuiz}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    <FileText size={16} className="mr-2" />
                    Take Quiz
                  </button>
                </div>
              </div>
            </>
          ) : (
            // Quiz content
            <div className="p-6">
              {!quizSubmitted ? (
                <>
                  <div className="mb-6">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                      Quiz: {selectedVideo.title}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                      Question {currentQuestion + 1} of {quizQuestions.length}
                    </p>
                    <div className="w-full bg-gray-200 rounded-full h-1.5 dark:bg-gray-700 mt-3">
                      <div 
                        className="bg-indigo-600 h-1.5 rounded-full transition-all duration-300" 
                        style={{ width: `${((currentQuestion + 1) / quizQuestions.length) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  <div className="mb-8">
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      {quizQuestions[currentQuestion].question}
                    </h4>
                    <div className="space-y-3">
                      {quizQuestions[currentQuestion].options.map((option, index) => (
                        <div 
                          key={index}
                          onClick={() => handleAnswerSelect(index)}
                          className={`p-3 border rounded-md cursor-pointer transition-colors ${
                            selectedAnswers[currentQuestion] === index
                              ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-900/30'
                              : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/50'
                          }`}
                        >
                          <div className="flex items-start">
                            <div className={`flex-shrink-0 h-5 w-5 border rounded-full mr-3 mt-0.5 flex items-center justify-center ${
                              selectedAnswers[currentQuestion] === index
                                ? 'border-indigo-500 bg-indigo-500'
                                : 'border-gray-400 dark:border-gray-500'
                            }`}>
                              {selectedAnswers[currentQuestion] === index && (
                                <div className="h-2 w-2 rounded-full bg-white"></div>
                              )}
                            </div>
                            <span className="text-gray-700 dark:text-gray-300">{option}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div className="flex justify-between">
                    <button
                      onClick={handlePrevQuestion}
                      disabled={currentQuestion === 0}
                      className="px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                    >
                      Previous
                    </button>
                    
                    {currentQuestion < quizQuestions.length - 1 ? (
                      <button
                        onClick={handleNextQuestion}
                        disabled={selectedAnswers[currentQuestion] === undefined}
                        className="px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                      >
                        Next
                      </button>
                    ) : (
                      <button
                        onClick={handleSubmitQuiz}
                        disabled={selectedAnswers.length < quizQuestions.length || selectedAnswers.includes(undefined)}
                        className="px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                      >
                        Submit Quiz
                      </button>
                    )}
                  </div>
                </>
              ) : (
                // Quiz results
                <div className="text-center py-8">
                  <div className="inline-flex items-center justify-center h-24 w-24 rounded-full bg-green-100 dark:bg-green-900 mb-6">
                    <CheckCircle size={48} className="text-green-600 dark:text-green-400" />
                  </div>
                  
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Quiz Completed!
                  </h3>
                  
                  <p className="text-lg text-gray-700 dark:text-gray-300 mb-6">
                    You scored {score} out of {quizQuestions.length}
                  </p>
                  
                  <div className="mb-8 max-w-md mx-auto">
                    <div className="w-full bg-gray-200 rounded-full h-3 dark:bg-gray-700">
                      <div 
                        className={`h-3 rounded-full ${
                          score >= quizQuestions.length * 0.7 
                            ? 'bg-green-500' 
                            : score >= quizQuestions.length * 0.4 
                              ? 'bg-yellow-500'
                              : 'bg-red-500'
                        }`} 
                        style={{ width: `${(score / quizQuestions.length) * 100}%` }}
                      ></div>
                    </div>
                    <div className="flex justify-between mt-2 text-sm text-gray-500 dark:text-gray-400">
                      <span>0</span>
                      <span>{quizQuestions.length}</span>
                    </div>
                  </div>
                  
                  <div className="flex flex-col sm:flex-row justify-center space-y-3 sm:space-y-0 sm:space-x-3">
                    <button
                      onClick={() => setShowQuiz(false)}
                      className="px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      Return to Video
                    </button>
                    <button
                      onClick={() => {
                        setShowQuiz(false);
                        const nextUnwatched = trainingVideos.find(video => !video.watched);
                        if (nextUnwatched) {
                          handleVideoSelect(nextUnwatched);
                        }
                      }}
                      className="px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      Next Training Video
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
        
        {/* Video list */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="font-medium text-gray-900 dark:text-white">
              Training Videos
            </h3>
          </div>
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {trainingVideos.map((video) => (
              <div 
                key={video.id}
                onClick={() => handleVideoSelect(video)}
                className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer ${
                  selectedVideo.id === video.id ? 'bg-indigo-50 dark:bg-indigo-900/30' : ''
                }`}
              >
                <div className="flex">
                  <div className="flex-shrink-0 relative mr-3">
                    <div className="aspect-video w-24 bg-gray-200 rounded overflow-hidden">
                      <img 
                        src={video.thumbnail} 
                        alt={video.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="absolute bottom-1 right-1 bg-gray-900/80 text-white text-xs px-1 rounded">
                      {video.duration}
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {video.title}
                    </h4>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 line-clamp-2">
                      {video.description}
                    </p>
                    {video.watched && (
                      <div className="mt-2 flex items-center text-green-600 dark:text-green-400 text-xs">
                        <CheckCircle size={12} className="mr-1" />
                        Completed
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default VolunteerTraining;