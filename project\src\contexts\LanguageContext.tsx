import React, { createContext, useContext, useState, useEffect } from 'react';

// Available languages
export type Language = 'en' | 'hi';

// Translation keys
export type TranslationKey = 
  | 'dashboard'
  | 'citizens'
  | 'training'
  | 'logout'
  | 'welcome'
  | 'citizensEnhanced'
  | 'schemeEligibility'
  | 'pendingReviews'
  | 'addCitizen'
  | 'name'
  | 'age'
  | 'gender'
  | 'male'
  | 'female'
  | 'other'
  | 'region'
  | 'caste'
  | 'income'
  | 'health'
  | 'education'
  | 'submit'
  | 'cancel'
  | 'uploadDocuments'
  | 'volunteerPerformance'
  | 'dataQuality'
  | 'regionCoverage'
  | 'schemeStatistics'
  | 'analytics'
  | 'reports'
  | 'settings'
  | 'login'
  | 'email'
  | 'password'
  | 'selectLanguage';

// Translation map
const translations: Record<Language, Record<TranslationKey, string>> = {
  en: {
    dashboard: 'Dashboard',
    citizens: 'Citizens',
    training: 'Training',
    logout: 'Logout',
    welcome: 'Welcome',
    citizensEnhanced: 'Citizens Enhanced',
    schemeEligibility: 'Scheme Eligibility',
    pendingReviews: 'Pending Reviews',
    addCitizen: 'Add Citizen',
    name: 'Name',
    age: 'Age',
    gender: 'Gender',
    male: 'Male',
    female: 'Female',
    other: 'Other',
    region: 'Region',
    caste: 'Caste',
    income: 'Annual Income',
    health: 'Health Status',
    education: 'Education Level',
    submit: 'Submit',
    cancel: 'Cancel',
    uploadDocuments: 'Upload Documents',
    volunteerPerformance: 'Volunteer Performance',
    dataQuality: 'Data Quality',
    regionCoverage: 'Region Coverage',
    schemeStatistics: 'Scheme Statistics',
    analytics: 'Analytics',
    reports: 'Reports',
    settings: 'Settings',
    login: 'Login',
    email: 'Email',
    password: 'Password',
    selectLanguage: 'Select Language'
  },
  hi: {
    dashboard: 'डैशबोर्ड',
    citizens: 'नागरिक',
    training: 'प्रशिक्षण',
    logout: 'लॉगआउट',
    welcome: 'स्वागत है',
    citizensEnhanced: 'नागरिक डेटा अपडेट',
    schemeEligibility: 'योजना पात्रता',
    pendingReviews: 'लंबित समीक्षा',
    addCitizen: 'नागरिक जोड़ें',
    name: 'नाम',
    age: 'आयु',
    gender: 'लिंग',
    male: 'पुरुष',
    female: 'महिला',
    other: 'अन्य',
    region: 'क्षेत्र',
    caste: 'जाति',
    income: 'वार्षिक आय',
    health: 'स्वास्थ्य स्थिति',
    education: 'शिक्षा स्तर',
    submit: 'सबमिट करें',
    cancel: 'रद्द करें',
    uploadDocuments: 'दस्तावेज अपलोड करें',
    volunteerPerformance: 'स्वयंसेवक प्रदर्शन',
    dataQuality: 'डेटा गुणवत्ता',
    regionCoverage: 'क्षेत्र कवरेज',
    schemeStatistics: 'योजना आँकड़े',
    analytics: 'विश्लेषण',
    reports: 'रिपोर्ट',
    settings: 'सेटिंग्स',
    login: 'लॉगिन',
    email: 'ईमेल',
    password: 'पासवर्ड',
    selectLanguage: 'भाषा चुनें'
  }
};

// Language context type
type LanguageContextType = {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: TranslationKey) => string;
};

// Create context
const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Provider component
export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>('en');

  // Load saved language preference on mount
  useEffect(() => {
    const savedLanguage = localStorage.getItem('citizen-connect-language') as Language;
    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'hi')) {
      setLanguageState(savedLanguage);
    }
  }, []);

  // Update language and save to local storage
  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
    localStorage.setItem('citizen-connect-language', lang);
  };

  // Translation function
  const t = (key: TranslationKey): string => {
    return translations[language][key] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

// Custom hook to use the language context
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};