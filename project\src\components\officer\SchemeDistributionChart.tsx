import React, { useEffect, useRef } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { Chart, registerables } from 'chart.js';

// Register Chart.js components
Chart.register(...registerables);

type SchemeDistributionChartProps = {
  chartType: 'age' | 'region' | 'schemes' | 'timeline';
};

// Mock data for charts
const mockData = {
  age: {
    labels: ['0-18', '19-30', '31-45', '46-60', '60+'],
    datasets: [
      {
        label: 'Citizens',
        data: [2100, 3800, 4200, 1950, 800],
        backgroundColor: [
          'rgba(59, 130, 246, 0.7)', // blue
          'rgba(16, 185, 129, 0.7)', // green
          'rgba(239, 68, 68, 0.7)',  // red
          'rgba(245, 158, 11, 0.7)', // yellow
          'rgba(139, 92, 246, 0.7)'  // purple
        ]
      }
    ]
  },
  region: {
    labels: ['Karnataka', 'Maharashtra', 'Tamil Nadu', 'Gujarat', 'Kerala'],
    datasets: [
      {
        label: 'Citizens',
        data: [3200, 4100, 2800, 1500, 1250],
        backgroundColor: [
          'rgba(59, 130, 246, 0.7)', // blue
          'rgba(16, 185, 129, 0.7)', // green
          'rgba(239, 68, 68, 0.7)',  // red
          'rgba(245, 158, 11, 0.7)', // yellow
          'rgba(139, 92, 246, 0.7)'  // purple
        ]
      }
    ]
  },
  schemes: {
    labels: ['PM Kisan Samman Nidhi', 'Ayushman Bharat', 'PM Awas Yojana', 'Sukanya Samriddhi Yojana', 'MGNREGA', 'Other Schemes'],
    datasets: [
      {
        label: 'Eligible',
        data: [5200, 7800, 4500, 2200, 6800, 3500],
        backgroundColor: 'rgba(59, 130, 246, 0.7)', // blue
      },
      {
        label: 'Enrolled',
        data: [4100, 6300, 3800, 1500, 5200, 2800],
        backgroundColor: 'rgba(16, 185, 129, 0.7)', // green
      }
    ]
  },
  timeline: {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    datasets: [
      {
        label: 'PM Kisan Samman Nidhi',
        data: [500, 580, 620, 750, 800, 950, 1100, 1250, 1380, 1450, 1520, 1600],
        borderColor: 'rgba(59, 130, 246, 0.7)', // blue
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true
      },
      {
        label: 'Ayushman Bharat',
        data: [800, 900, 1000, 1150, 1300, 1450, 1600, 1800, 2000, 2200, 2400, 2600],
        borderColor: 'rgba(16, 185, 129, 0.7)', // green
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        tension: 0.4,
        fill: true
      },
      {
        label: 'PM Awas Yojana',
        data: [300, 350, 380, 430, 500, 580, 650, 720, 790, 850, 920, 1000],
        borderColor: 'rgba(239, 68, 68, 0.7)', // red
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        tension: 0.4,
        fill: true
      }
    ]
  }
};

// Chart configurations
const chartConfigs = {
  age: {
    type: 'pie',
    options: {
      plugins: {
        legend: {
          position: 'right'
        }
      }
    }
  },
  region: {
    type: 'doughnut',
    options: {
      plugins: {
        legend: {
          position: 'right'
        }
      }
    }
  },
  schemes: {
    type: 'bar',
    options: {
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  },
  timeline: {
    type: 'line',
    options: {
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  }
};

const SchemeDistributionChart: React.FC<SchemeDistributionChartProps> = ({ chartType }) => {
  const chartRef = useRef<HTMLCanvasElement>(null);
  const chartInstance = useRef<Chart>();
  const { theme } = useTheme();
  
  useEffect(() => {
    if (chartRef.current) {
      // Destroy existing chart to prevent memory leaks
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
      
      // Adjust colors based on theme
      const textColor = theme === 'dark' ? '#d1d5db' : '#4b5563';
      const gridColor = theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
      
      const ctx = chartRef.current.getContext('2d');
      if (ctx) {
        // Get data and config for the chart type
        const data = mockData[chartType];
        const config = chartConfigs[chartType];
        
        // Create chart with merged config options
        chartInstance.current = new Chart(ctx, {
          type: config.type as any,
          data: data,
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: config.options.plugins?.legend?.position || 'top',
                labels: {
                  color: textColor,
                  font: {
                    size: 12
                  },
                  usePointStyle: true
                }
              },
              tooltip: {
                backgroundColor: theme === 'dark' ? '#374151' : 'rgba(255, 255, 255, 0.9)',
                titleColor: theme === 'dark' ? '#f3f4f6' : '#1f2937',
                bodyColor: theme === 'dark' ? '#d1d5db' : '#4b5563',
                borderColor: theme === 'dark' ? '#4b5563' : '#e5e7eb',
                borderWidth: 1,
                padding: 10,
                cornerRadius: 4,
                displayColors: true,
                usePointStyle: true,
              }
            },
            scales: config.options.scales ? {
              x: {
                grid: {
                  display: false
                },
                ticks: {
                  color: textColor
                }
              },
              y: {
                beginAtZero: true,
                grid: {
                  color: gridColor
                },
                ticks: {
                  color: textColor
                }
              }
            } : undefined
          }
        });
      }
    }
    
    // Cleanup chart on unmount
    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [theme, chartType]);
  
  return <canvas ref={chartRef} />;
};

export default SchemeDistributionChart;