import React, { createContext, useContext, useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';

export type User = {
  id: string;
  name: string;
  email: string;
  role: 'volunteer' | 'supervisor' | 'officer';
  region?: string;
  profilePic?: string;
};

export type AuthContextType = {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => void;
};

// Mock user data for demonstration
const mockUsers = [
  {
    id: '1',
    name: 'Volunteer User',
    email: '<EMAIL>',
    password: 'password123',
    role: 'volunteer',
    region: 'Karnataka',
    profilePic: 'https://i.pravatar.cc/150?img=1'
  },
  {
    id: '2',
    name: 'Supervisor User',
    email: '<EMAIL>',
    password: 'password123',
    role: 'supervisor',
    region: 'Maharashtra',
    profilePic: 'https://i.pravatar.cc/150?img=2'
  },
  {
    id: '3',
    name: 'Officer User',
    email: '<EMAIL>',
    password: 'password123',
    role: 'officer',
    region: 'National',
    profilePic: 'https://i.pravatar.cc/150?img=3'
  }
];

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Check for saved user on initial load
  useEffect(() => {
    const savedUser = localStorage.getItem('citizen-connect-user');
    if (savedUser) {
      setUser(JSON.parse(savedUser));
    }
    setLoading(false);
  }, []);

  const login = async (email: string, password: string) => {
    setLoading(true);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const foundUser = mockUsers.find(u => u.email === email && u.password === password);
    
    if (foundUser) {
      // Remove password before storing
      const { password, ...safeUserData } = foundUser;
      setUser(safeUserData as User);
      localStorage.setItem('citizen-connect-user', JSON.stringify(safeUserData));
      toast.success(`Welcome back, ${safeUserData.name}!`);
    } else {
      toast.error('Invalid email or password');
    }
    
    setLoading(false);
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('citizen-connect-user');
    toast.success('Successfully logged out');
  };

  const updateUser = (userData: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      localStorage.setItem('citizen-connect-user', JSON.stringify(updatedUser));
    }
  };

  return (
    <AuthContext.Provider value={{ user, loading, login, logout, updateUser }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};