import React from 'react';
import { TrendingUp, TrendingDown } from 'lucide-react';

type StatCardProps = {
  title: string;
  value: number;
  icon: React.ReactNode;
  change?: number;
  period?: string;
};

const StatCard: React.FC<StatCardProps> = ({ 
  title, 
  value, 
  icon, 
  change = 0,
  period = 'vs. previous period'
}) => {
  const isPositive = change >= 0;
  
  return (
    <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
      <div className="flex items-center">
        <div className="flex-shrink-0 h-10 w-10 rounded-md bg-indigo-600 flex items-center justify-center text-white">
          {icon}
        </div>
        <div className="ml-4">
          <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
            {title}
          </h4>
          <div className="flex items-baseline">
            <p className="text-2xl font-semibold text-gray-900 dark:text-white">
              {value.toLocaleString()}
            </p>
            {change !== 0 && (
              <p className={`ml-2 flex items-baseline text-sm font-semibold ${
                isPositive 
                  ? 'text-green-600 dark:text-green-400' 
                  : 'text-red-600 dark:text-red-400'
              }`}>
                <span className="mr-1">
                  {isPositive ? <TrendingUp size={16} /> : <TrendingDown size={16} />}
                </span>
                {isPositive ? '+' : ''}{change}%
              </p>
            )}
          </div>
          {period && (
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              {period}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default StatCard;