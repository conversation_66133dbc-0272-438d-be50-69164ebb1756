import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { Toaster } from 'react-hot-toast';
import { ThemeProvider } from './contexts/ThemeContext';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { LanguageProvider } from './contexts/LanguageContext';

// Layouts
import MainLayout from './layouts/MainLayout';

// Pages
import Login from './pages/Login';
import VolunteerDashboard from './pages/volunteer/Dashboard';
import CitizenForm from './pages/volunteer/CitizenForm';
import VolunteerTraining from './pages/volunteer/Training';
import SupervisorDashboard from './pages/supervisor/Dashboard';
import DataQuality from './pages/supervisor/DataQuality';
import OfficerDashboard from './pages/officer/Dashboard';
import Analytics from './pages/officer/Analytics';
import Reports from './pages/officer/Reports';
import Settings from './pages/officer/Settings';
import NotFound from './pages/NotFound';

// Protected route component
const ProtectedRoute = ({ 
  children, 
  allowedRoles,
}: { 
  children: React.ReactNode, 
  allowedRoles: string[] 
}) => {
  const { user, loading } = useAuth();

  if (loading) {
    return <div className="flex items-center justify-center h-screen">Loading...</div>;
  }

  if (!user || !allowedRoles.includes(user.role)) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

function App() {
  return (
    <ThemeProvider>
      <LanguageProvider>
        <AuthProvider>
          <Router>
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 3000,
                style: {
                  background: '#363636',
                  color: '#fff',
                },
              }}
            />
            <Routes>
              <Route path="/login" element={<Login />} />
              
              {/* Volunteer Routes */}
              <Route 
                path="/volunteer" 
                element={
                  <ProtectedRoute allowedRoles={['volunteer']}>
                    <MainLayout />
                  </ProtectedRoute>
                }
              >
                <Route index element={<VolunteerDashboard />} />
                <Route path="citizen-form" element={<CitizenForm />} />
                <Route path="training" element={<VolunteerTraining />} />
              </Route>
              
              {/* Supervisor Routes */}
              <Route 
                path="/supervisor" 
                element={
                  <ProtectedRoute allowedRoles={['supervisor']}>
                    <MainLayout />
                  </ProtectedRoute>
                }
              >
                <Route index element={<SupervisorDashboard />} />
                <Route path="data-quality" element={<DataQuality />} />
              </Route>
              
              {/* Officer Routes */}
              <Route 
                path="/officer" 
                element={
                  <ProtectedRoute allowedRoles={['officer']}>
                    <MainLayout />
                  </ProtectedRoute>
                }
              >
                <Route index element={<OfficerDashboard />} />
                <Route path="analytics" element={<Analytics />} />
                <Route path="reports" element={<Reports />} />
                <Route path="settings" element={<Settings />} />
              </Route>
              
              {/* Default route redirect */}
              <Route path="/" element={<Navigate to="/login" replace />} />
              
              {/* 404 route */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </Router>
        </AuthProvider>
      </LanguageProvider>
    </ThemeProvider>
  );
}

export default App;