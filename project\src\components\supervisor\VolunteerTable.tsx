import React, { useState } from 'react';
import { CheckCircle, AlertCircle, ArrowUpDown, ChevronUp, ChevronDown, User } from 'lucide-react';

// Mock volunteer data
const mockVolunteers = [
  {
    id: '1',
    name: '<PERSON><PERSON>',
    region: 'Karnataka',
    citizensProcessed: 42,
    pendingReviews: 5,
    qualityScore: 94,
    activeStatus: 'active',
    lastActive: '2 hours ago'
  },
  {
    id: '2',
    name: '<PERSON><PERSON>',
    region: 'Karnataka',
    citizensProcessed: 37,
    pendingReviews: 2,
    qualityScore: 88,
    activeStatus: 'active',
    lastActive: '1 hour ago'
  },
  {
    id: '3',
    name: '<PERSON><PERSON> <PERSON>',
    region: 'Maharashtra',
    citizensProcessed: 56,
    pendingReviews: 0,
    qualityScore: 97,
    activeStatus: 'active',
    lastActive: 'Just now'
  },
  {
    id: '4',
    name: '<PERSON><PERSON><PERSON>',
    region: 'Tamil Nadu',
    citizensProcessed: 28,
    pendingReviews: 8,
    qualityScore: 82,
    activeStatus: 'inactive',
    lastActive: '1 day ago'
  },
  {
    id: '5',
    name: '<PERSON><PERSON><PERSON>',
    region: 'Karnataka',
    citizensProcessed: 34,
    pendingReviews: 3,
    qualityScore: 91,
    activeStatus: 'active',
    lastActive: '5 hours ago'
  }
];

type SortField = 'name' | 'region' | 'citizensProcessed' | 'pendingReviews' | 'qualityScore';
type SortDirection = 'asc' | 'desc';

const VolunteerTable: React.FC = () => {
  const [sortField, setSortField] = useState<SortField>('qualityScore');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  
  const handleSort = (field: SortField) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };
  
  // Sort volunteers based on current sort settings
  const sortedVolunteers = [...mockVolunteers].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortDirection === 'asc' 
        ? aValue.localeCompare(bValue) 
        : bValue.localeCompare(aValue);
    }
    
    return sortDirection === 'asc' 
      ? (aValue as number) - (bValue as number) 
      : (bValue as number) - (aValue as number);
  });
  
  // Render sort indicator
  const renderSortIndicator = (field: SortField) => {
    if (field !== sortField) {
      return <ArrowUpDown size={16} className="ml-1 text-gray-400" />;
    }
    
    return sortDirection === 'asc' 
      ? <ChevronUp size={16} className="ml-1 text-indigo-600 dark:text-indigo-400" />
      : <ChevronDown size={16} className="ml-1 text-indigo-600 dark:text-indigo-400" />;
  };
  
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead className="bg-gray-50 dark:bg-gray-700/50">
          <tr>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
              onClick={() => handleSort('name')}
            >
              <div className="flex items-center">
                Volunteer {renderSortIndicator('name')}
              </div>
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
              onClick={() => handleSort('region')}
            >
              <div className="flex items-center">
                Region {renderSortIndicator('region')}
              </div>
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
              onClick={() => handleSort('citizensProcessed')}
            >
              <div className="flex items-center">
                Citizens {renderSortIndicator('citizensProcessed')}
              </div>
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
              onClick={() => handleSort('pendingReviews')}
            >
              <div className="flex items-center">
                Pending {renderSortIndicator('pendingReviews')}
              </div>
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
              onClick={() => handleSort('qualityScore')}
            >
              <div className="flex items-center">
                Quality {renderSortIndicator('qualityScore')}
              </div>
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
            >
              Status
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
            >
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          {sortedVolunteers.map((volunteer) => (
            <tr 
              key={volunteer.id} 
              className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
            >
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 dark:bg-indigo-900/50 flex items-center justify-center text-indigo-600 dark:text-indigo-400">
                    <User size={18} />
                  </div>
                  <div className="ml-4">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {volunteer.name}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      Last active: {volunteer.lastActive}
                    </div>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900 dark:text-white">{volunteer.region}</div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900 dark:text-white">{volunteer.citizensProcessed}</div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm">
                  {volunteer.pendingReviews > 0 ? (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                      {volunteer.pendingReviews} pending
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      All clear
                    </span>
                  )}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700 mr-2 max-w-[100px]">
                    <div 
                      className={`h-2 rounded-full ${
                        volunteer.qualityScore >= 90 
                          ? 'bg-green-500' 
                          : volunteer.qualityScore >= 70 
                            ? 'bg-yellow-500'
                            : 'bg-red-500'
                      }`} 
                      style={{ width: `${volunteer.qualityScore}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-900 dark:text-white">
                    {volunteer.qualityScore}%
                  </span>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  {volunteer.activeStatus === 'active' ? (
                    <>
                      <div className="h-2.5 w-2.5 rounded-full bg-green-500 mr-2"></div>
                      <span className="text-sm text-gray-900 dark:text-white">Active</span>
                    </>
                  ) : (
                    <>
                      <div className="h-2.5 w-2.5 rounded-full bg-gray-400 dark:bg-gray-500 mr-2"></div>
                      <span className="text-sm text-gray-500 dark:text-gray-400">Inactive</span>
                    </>
                  )}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <a href="#" className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300">
                  Details
                </a>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default VolunteerTable;