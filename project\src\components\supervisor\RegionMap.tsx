import React, { useEffect, useRef } from 'react';
import { useTheme } from '../../contexts/ThemeContext';

const RegionMap: React.FC = () => {
  const mapRef = useRef<HTMLDivElement>(null);
  const { theme } = useTheme();
  
  useEffect(() => {
    if (mapRef.current) {
      // Simulate a map by displaying a placeholder image
      // In a real app, this would be replaced with a mapping library like Leaflet
      const mapContainer = mapRef.current;
      mapContainer.innerHTML = '';
      
      // Create an SVG to represent a simplified India map with regions
      const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
      svg.setAttribute('width', '100%');
      svg.setAttribute('height', '100%');
      svg.setAttribute('viewBox', '0 0 300 300');
      svg.style.backgroundColor = theme === 'dark' ? '#1f2937' : '#f3f4f6';
      
      // Create stylized regions (simplified shapes for demonstration)
      const regions = [
        { id: 'region1', d: 'M50,50 L100,50 L120,80 L80,120 Z', name: 'Karnataka', coverage: 92 },
        { id: 'region2', d: 'M120,80 L150,60 L180,90 L130,130 Z', name: 'Maharashtra', coverage: 78 },
        { id: 'region3', d: 'M80,120 L130,130 L110,180 L60,160 Z', name: 'Tamil Nadu', coverage: 65 },
        { id: 'region4', d: 'M130,130 L180,90 L200,130 L170,170 Z', name: 'Gujarat', coverage: 45 },
        { id: 'region5', d: 'M110,180 L170,170 L180,210 L120,220 Z', name: 'Kerala', coverage: 82 }
      ];
      
      // Get color based on coverage
      const getColor = (coverage: number) => {
        if (coverage >= 90) return '#10b981'; // green
        if (coverage >= 70) return '#3b82f6'; // blue
        if (coverage >= 40) return '#f59e0b'; // yellow
        return '#ef4444'; // red
      };
      
      // Add regions to SVG
      regions.forEach(region => {
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path.setAttribute('d', region.d);
        path.setAttribute('fill', getColor(region.coverage));
        path.setAttribute('stroke', theme === 'dark' ? '#374151' : '#e5e7eb');
        path.setAttribute('stroke-width', '2');
        path.setAttribute('id', region.id);
        
        // Add title for tooltip on hover
        const title = document.createElementNS('http://www.w3.org/2000/svg', 'title');
        title.textContent = `${region.name}: ${region.coverage}% coverage`;
        path.appendChild(title);
        
        // Add hover effect
        path.onmouseover = () => {
          path.setAttribute('fill-opacity', '0.8');
          path.setAttribute('stroke-width', '3');
          
          // Show region info
          const regionInfo = document.getElementById('region-info');
          if (regionInfo) {
            regionInfo.textContent = `${region.name}: ${region.coverage}% coverage`;
          }
        };
        
        path.onmouseout = () => {
          path.setAttribute('fill-opacity', '1');
          path.setAttribute('stroke-width', '2');
          
          // Clear region info
          const regionInfo = document.getElementById('region-info');
          if (regionInfo) {
            regionInfo.textContent = 'Hover over a region';
          }
        };
        
        svg.appendChild(path);
      });
      
      // Add region info text
      const textElement = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      textElement.setAttribute('x', '10');
      textElement.setAttribute('y', '280');
      textElement.setAttribute('fill', theme === 'dark' ? '#d1d5db' : '#4b5563');
      textElement.setAttribute('font-size', '12');
      textElement.setAttribute('id', 'region-info');
      textElement.textContent = 'Hover over a region';
      svg.appendChild(textElement);
      
      // Add legend
      const legendY = 240;
      const addLegendItem = (x: number, color: string, text: string) => {
        const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        rect.setAttribute('x', x.toString());
        rect.setAttribute('y', legendY.toString());
        rect.setAttribute('width', '10');
        rect.setAttribute('height', '10');
        rect.setAttribute('fill', color);
        
        const label = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        label.setAttribute('x', (x + 15).toString());
        label.setAttribute('y', (legendY + 8).toString());
        label.setAttribute('fill', theme === 'dark' ? '#d1d5db' : '#4b5563');
        label.setAttribute('font-size', '10');
        label.textContent = text;
        
        svg.appendChild(rect);
        svg.appendChild(label);
      };
      
      addLegendItem(10, '#10b981', 'High');
      addLegendItem(70, '#3b82f6', 'Medium');
      addLegendItem(140, '#f59e0b', 'Low');
      addLegendItem(190, '#ef4444', 'Critical');
      
      mapContainer.appendChild(svg);
    }
  }, [theme]);
  
  return (
    <div ref={mapRef} className="w-full h-full"></div>
  );
};

export default RegionMap;