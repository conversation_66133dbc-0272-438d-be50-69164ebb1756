import React, { useEffect, useRef } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { Chart, registerables } from 'chart.js';

// Register Chart.js components
Chart.register(...registerables);

// Mock data for the chart
const mockData = {
  labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
  datasets: [
    {
      label: 'Citizens Added',
      data: [5, 8, 3, 6, 9, 4, 7],
      backgroundColor: '#4f46e5',
      borderColor: '#4f46e5',
      borderWidth: 2,
      borderRadius: 4,
      barThickness: 16,
    },
    {
      label: 'Documents Processed',
      data: [3, 5, 2, 4, 7, 3, 5],
      backgroundColor: '#818cf8',
      borderColor: '#818cf8',
      borderWidth: 2,
      borderRadius: 4,
      barThickness: 16,
    }
  ]
};

const ProgressChart: React.FC = () => {
  const chartRef = useRef<HTMLCanvasElement>(null);
  const chartInstance = useRef<Chart>();
  const { theme } = useTheme();
  
  useEffect(() => {
    if (chartRef.current) {
      // Destroy existing chart to prevent memory leaks
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
      
      // Adjust colors based on theme
      const textColor = theme === 'dark' ? '#d1d5db' : '#4b5563';
      const gridColor = theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
      
      const ctx = chartRef.current.getContext('2d');
      if (ctx) {
        chartInstance.current = new Chart(ctx, {
          type: 'bar',
          data: mockData,
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'top',
                labels: {
                  color: textColor,
                  font: {
                    size: 12
                  }
                }
              },
              tooltip: {
                backgroundColor: theme === 'dark' ? '#374151' : 'rgba(255, 255, 255, 0.9)',
                titleColor: theme === 'dark' ? '#f3f4f6' : '#1f2937',
                bodyColor: theme === 'dark' ? '#d1d5db' : '#4b5563',
                borderColor: theme === 'dark' ? '#4b5563' : '#e5e7eb',
                borderWidth: 1,
                padding: 10,
                cornerRadius: 4,
                displayColors: true,
                usePointStyle: true,
              }
            },
            scales: {
              x: {
                grid: {
                  display: false
                },
                ticks: {
                  color: textColor
                }
              },
              y: {
                beginAtZero: true,
                grid: {
                  color: gridColor
                },
                ticks: {
                  color: textColor,
                  stepSize: 2
                }
              }
            }
          }
        });
      }
    }
    
    // Cleanup chart on unmount
    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [theme]);
  
  return <canvas ref={chartRef} />;
};

export default ProgressChart;