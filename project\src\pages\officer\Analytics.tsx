import React, { useState } from 'react';
import { BarChart2, TrendingUp, Users, Map } from 'lucide-react';
import SchemeDistributionChart from '../../components/officer/SchemeDistributionChart';
import RegionHeatmap from '../../components/officer/RegionHeatmap';
import DemographicFilter from '../../components/officer/DemographicFilter';

const Analytics = () => {
  const [activeView, setActiveView] = useState('overview');
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>({});
  
  // Filter options
  const filterOptions = {
    ageGroups: ['0-18', '19-30', '31-45', '46-60', '60+'],
    genders: ['Male', 'Female', 'Other'],
    regions: ['Karnataka', 'Maharashtra', 'Tamil Nadu', 'Gujarat', 'Kerala'],
    castes: ['General', 'OBC', 'SC', 'ST'],
    incomeRanges: ['Below 50k', '50k-1L', '1L-2.5L', '2.5L-5L', 'Above 5L']
  };
  
  // Update active filters
  const handleFilterChange = (filterType: string, values: string[]) => {
    setActiveFilters(prev => ({
      ...prev,
      [filterType]: values
    }));
  };
  
  // Clear all filters
  const clearFilters = () => {
    setActiveFilters({});
  };
  
  // Count active filters
  const activeFilterCount = Object.values(activeFilters).reduce(
    (count, values) => count + values.length, 
    0
  );
  
  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Analytics Dashboard
          </h2>
          
          <div className="mt-4 sm:mt-0 flex space-x-2">
            <button
              onClick={() => setShowFilterPanel(true)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 relative"
            >
              <Users size={16} className="mr-2" />
              Demographic Filters
              {activeFilterCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-indigo-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {activeFilterCount}
                </span>
              )}
            </button>
          </div>
        </div>
        
        <div className="mt-6">
          <nav className="flex space-x-4">
            <button
              onClick={() => setActiveView('overview')}
              className={`px-3 py-2 text-sm font-medium rounded-md ${
                activeView === 'overview'
                  ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-200'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <BarChart2 size={16} className="inline-block mr-2" />
              Overview
            </button>
            <button
              onClick={() => setActiveView('trends')}
              className={`px-3 py-2 text-sm font-medium rounded-md ${
                activeView === 'trends'
                  ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-200'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <TrendingUp size={16} className="inline-block mr-2" />
              Trends
            </button>
            <button
              onClick={() => setActiveView('regions')}
              className={`px-3 py-2 text-sm font-medium rounded-md ${
                activeView === 'regions'
                  ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-200'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <Map size={16} className="inline-block mr-2" />
              Regional Analysis
            </button>
          </nav>
        </div>
      </div>
      
      {/* Content based on active view */}
      {activeView === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Age Distribution
            </h3>
            <div className="h-80">
              <SchemeDistributionChart chartType="age" />
            </div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Regional Distribution
            </h3>
            <div className="h-80">
              <SchemeDistributionChart chartType="region" />
            </div>
          </div>
        </div>
      )}
      
      {activeView === 'trends' && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Scheme Enrollment Trends
          </h3>
          <div className="h-96">
            <SchemeDistributionChart chartType="timeline" />
          </div>
        </div>
      )}
      
      {activeView === 'regions' && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Regional Coverage Analysis
          </h3>
          <div className="h-96">
            <RegionHeatmap />
          </div>
        </div>
      )}
      
      {/* Filter panel */}
      {showFilterPanel && (
        <>
          <div className="fixed inset-0 bg-black bg-opacity-25 z-40" onClick={() => setShowFilterPanel(false)} />
          <div className="fixed inset-y-0 right-0 w-80 bg-white dark:bg-gray-800 shadow-lg z-50 overflow-y-auto">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Demographic Filters
                </h3>
                <button
                  onClick={() => setShowFilterPanel(false)}
                  className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
                >
                  &times;
                </button>
              </div>
            </div>
            
            <div className="p-4">
              <DemographicFilter
                options={filterOptions}
                activeFilters={activeFilters}
                onFilterChange={handleFilterChange}
              />
              
              <div className="mt-6 flex space-x-2">
                <button
                  onClick={clearFilters}
                  className="flex-1 px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 focus:outline-none"
                >
                  Clear All
                </button>
                <button
                  onClick={() => setShowFilterPanel(false)}
                  className="flex-1 px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none"
                >
                  Apply Filters
                </button>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default Analytics;