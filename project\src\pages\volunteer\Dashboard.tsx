import { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { UserPlus, Award, BarChart, FileText } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import StatCard from '../../components/dashboard/StatCard';
import ProgressChart from '../../components/charts/ProgressChart';
import EligibilityList from '../../components/dashboard/EligibilityList';

// Mock citizen data
const mockCitizensData = {
  total: 42,
  pending: 5,
  approved: 37,
  today: 3,
  schemeEligible: 28
};

// Mock schemes with region-specific eligibility
const mockSchemes = [
  { 
    id: '1', 
    name: 'PM <PERSON><PERSON>', 
    eligibleCount: 18, 
    icon: '🌾',
    eligibleCitizens: [
      { id: '1', name: '<PERSON><PERSON>', age: 45, income: '50,000 - 1,00,000' },
      { id: '2', name: '<PERSON><PERSON>', age: 38, income: 'Below 50,000' }
    ]
  },
  { 
    id: '2', 
    name: '<PERSON><PERSON><PERSON><PERSON>', 
    eligibleCount: 23, 
    icon: '🏥',
    eligibleCitizens: [
      { id: '3', name: 'Amit <PERSON>', age: 52, income: '1,00,000 - 2,50,000' },
      { id: '4', name: 'Neha Sharma', age: 35, income: 'Below 50,000' }
    ]
  },
  { 
    id: '3', 
    name: 'PM Awas Yojana', 
    eligibleCount: 12, 
    icon: '🏠',
    eligibleCitizens: [
      { id: '5', name: 'Vikram Reddy', age: 42, income: '50,000 - 1,00,000' }
    ]
  },
  { 
    id: '4', 
    name: 'Sukanya Samriddhi Yojana', 
    eligibleCount: 7, 
    icon: '👧',
    eligibleCitizens: [
      { id: '6', name: 'Meera Gupta', age: 28, income: '1,00,000 - 2,50,000' }
    ]
  }
];

// Mock recent activity
const mockActivity = [
  { id: '1', action: 'Added new citizen', name: 'Rahul Kumar', time: '2 hours ago' },
  { id: '2', action: 'Updated information', name: 'Priya Singh', time: '5 hours ago' },
  { id: '3', action: 'Document uploaded', name: 'Amit Patel', time: 'Yesterday' }
];

const VolunteerDashboard = () => {
  const { user } = useAuth();
  const { t } = useLanguage();
  const [isLoading, setIsLoading] = useState(true);
  const [selectedScheme, setSelectedScheme] = useState<string | null>(null);
  
  useEffect(() => {
    // Simulate loading data
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);
    
    return () => clearTimeout(timer);
  }, []);
  
  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    );
  }
  
  // Filter schemes based on volunteer's region
  const regionSchemes = mockSchemes.map(scheme => ({
    ...scheme,
    eligibleCount: scheme.eligibleCitizens.filter(citizen => 
      // In a real app, this would check against actual region data
      Math.random() > 0.5 // Simulated region check
    ).length
  }));
  
  return (
    <div className="space-y-6">
      {/* Welcome section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="bg-gradient-to-r from-indigo-600 to-blue-500 px-6 py-4">
          <h2 className="text-xl font-semibold text-white">
            {t('welcome')}, {user?.name}!
          </h2>
          <p className="text-indigo-100 mt-1">
            {user?.region} Region
          </p>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <StatCard 
              title={t('citizensEnhanced')} 
              value={mockCitizensData.total} 
              icon={<UserPlus />} 
              change={+8} 
              period="This week" 
            />
            <StatCard 
              title={t('schemeEligibility')} 
              value={mockCitizensData.schemeEligible} 
              icon={<Award />} 
              change={+12} 
              period="This week" 
            />
            <StatCard 
              title={t('pendingReviews')} 
              value={mockCitizensData.pending} 
              icon={<FileText />} 
              change={-2} 
              period="This week" 
            />
          </div>
          
          <div className="mt-6">
            <Link 
              to="/volunteer/citizen-form" 
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <UserPlus size={16} className="mr-2" />
              {t('addCitizen')}
            </Link>
          </div>
        </div>
      </div>
      
      {/* Middle section - Charts and Eligibility */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Progress Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            <div className="flex items-center">
              <BarChart size={20} className="mr-2 text-indigo-600" />
              Weekly Progress
            </div>
          </h3>
          <div className="h-60">
            <ProgressChart />
          </div>
        </div>
        
        {/* Scheme Eligibility */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            <div className="flex items-center">
              <Award size={20} className="mr-2 text-indigo-600" />
              Scheme Eligibility
            </div>
          </h3>
          <div className="space-y-4">
            {regionSchemes.map((scheme) => (
              <div key={scheme.id}>
                <div 
                  className="flex items-center p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors cursor-pointer"
                  onClick={() => setSelectedScheme(selectedScheme === scheme.id ? null : scheme.id)}
                >
                  <div className="h-10 w-10 rounded-full bg-indigo-100 dark:bg-indigo-900/50 flex items-center justify-center text-2xl mr-4">
                    {scheme.icon}
                  </div>
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                      {scheme.name}
                    </h4>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {scheme.eligibleCount} eligible citizens in your region
                    </p>
                  </div>
                  <div className="text-right">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      Eligible
                    </span>
                  </div>
                </div>
                
                {/* Eligible Citizens List */}
                {selectedScheme === scheme.id && scheme.eligibleCitizens.length > 0 && (
                  <div className="mt-2 ml-14 space-y-2">
                    {scheme.eligibleCitizens.map(citizen => (
                      <div 
                        key={citizen.id}
                        className="p-2 bg-gray-50 dark:bg-gray-700/50 rounded-md text-sm"
                      >
                        <div className="font-medium text-gray-900 dark:text-white">
                          {citizen.name}
                        </div>
                        <div className="text-gray-500 dark:text-gray-400 text-xs">
                          Age: {citizen.age} • Income: {citizen.income}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {/* Recent Activity */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Recent Activity
        </h3>
        <div className="space-y-4">
          {mockActivity.map((activity) => (
            <div 
              key={activity.id}
              className="flex items-start border-b border-gray-200 dark:border-gray-700 pb-3 last:border-0 last:pb-0"
            >
              <div className="h-8 w-8 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center text-indigo-600 dark:text-indigo-300 mr-3">
                {activity.action.includes('Added') ? (
                  <UserPlus size={16} />
                ) : activity.action.includes('Updated') ? (
                  <FileText size={16} />
                ) : (
                  <Award size={16} />
                )}
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {activity.action}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {activity.name} • {activity.time}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default VolunteerDashboard;