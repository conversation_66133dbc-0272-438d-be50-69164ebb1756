import React, { useEffect, useRef } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { Chart, registerables } from 'chart.js';

// Register Chart.js components
Chart.register(...registerables);

// Mock data for the chart
const mockData = {
  labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
  datasets: [
    {
      label: 'Data Completeness',
      data: [78, 82, 85, 87],
      borderColor: '#10b981',
      backgroundColor: 'rgba(16, 185, 129, 0.1)',
      tension: 0.4,
      fill: true,
    },
    {
      label: 'Verification Rate',
      data: [65, 68, 70, 72],
      borderColor: '#3b82f6',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.4,
      fill: true,
    },
    {
      label: 'Error Rate',
      data: [12, 9, 7, 5],
      borderColor: '#ef4444',
      backgroundColor: 'rgba(239, 68, 68, 0.1)',
      tension: 0.4,
      fill: true,
    }
  ]
};

const QualityChart: React.FC = () => {
  const chartRef = useRef<HTMLCanvasElement>(null);
  const chartInstance = useRef<Chart>();
  const { theme } = useTheme();
  
  useEffect(() => {
    if (chartRef.current) {
      // Destroy existing chart to prevent memory leaks
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
      
      // Adjust colors based on theme
      const textColor = theme === 'dark' ? '#d1d5db' : '#4b5563';
      const gridColor = theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
      
      const ctx = chartRef.current.getContext('2d');
      if (ctx) {
        chartInstance.current = new Chart(ctx, {
          type: 'line',
          data: mockData,
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'top',
                labels: {
                  color: textColor,
                  font: {
                    size: 12
                  },
                  usePointStyle: true
                }
              },
              tooltip: {
                backgroundColor: theme === 'dark' ? '#374151' : 'rgba(255, 255, 255, 0.9)',
                titleColor: theme === 'dark' ? '#f3f4f6' : '#1f2937',
                bodyColor: theme === 'dark' ? '#d1d5db' : '#4b5563',
                borderColor: theme === 'dark' ? '#4b5563' : '#e5e7eb',
                borderWidth: 1,
                padding: 10,
                cornerRadius: 4,
                displayColors: true,
                usePointStyle: true,
              }
            },
            scales: {
              x: {
                grid: {
                  display: false
                },
                ticks: {
                  color: textColor
                }
              },
              y: {
                beginAtZero: true,
                grid: {
                  color: gridColor
                },
                ticks: {
                  color: textColor,
                  callback: function(value) {
                    return value + '%';
                  }
                },
                max: 100
              }
            }
          }
        });
      }
    }
    
    // Cleanup chart on unmount
    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [theme]);
  
  return <canvas ref={chartRef} />;
};

export default QualityChart;