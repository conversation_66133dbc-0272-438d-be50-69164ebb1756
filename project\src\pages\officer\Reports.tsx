import React, { useState } from 'react';
import { Download, Filter, FileText, BarChart2, Calendar } from 'lucide-react';
import SchemeDistributionChart from '../../components/officer/SchemeDistributionChart';

const Reports = () => {
  const [selectedReport, setSelectedReport] = useState('scheme-distribution');
  const [dateRange, setDateRange] = useState('last-30');
  
  const reports = [
    {
      id: 'scheme-distribution',
      name: 'Scheme Distribution Report',
      description: 'Analysis of citizen distribution across various government schemes',
      icon: BarChart2
    },
    {
      id: 'demographic',
      name: 'Demographic Report',
      description: 'Detailed breakdown of citizen demographics and eligibility',
      icon: FileText
    },
    {
      id: 'monthly',
      name: 'Monthly Progress Report',
      description: 'Month-over-month progress in citizen data enhancement',
      icon: Calendar
    }
  ];
  
  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Reports & Analytics
          </h2>
          
          <div className="mt-4 sm:mt-0 flex space-x-2">
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
            >
              <option value="last-7">Last 7 days</option>
              <option value="last-30">Last 30 days</option>
              <option value="last-90">Last 90 days</option>
              <option value="year-to-date">Year to date</option>
            </select>
            
            <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              <Download size={16} className="mr-2" />
              Export Report
            </button>
          </div>
        </div>
        
        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
          {reports.map((report) => (
            <div
              key={report.id}
              onClick={() => setSelectedReport(report.id)}
              className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                selectedReport === report.id
                  ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-900/30'
                  : 'border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50'
              }`}
            >
              <div className="flex items-start">
                <div className={`flex-shrink-0 h-10 w-10 rounded-lg flex items-center justify-center ${
                  selectedReport === report.id
                    ? 'bg-indigo-500 text-white'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400'
                }`}>
                  <report.icon size={20} />
                </div>
                <div className="ml-4">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                    {report.name}
                  </h3>
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    {report.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Report Content */}
      {selectedReport === 'scheme-distribution' && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Scheme Distribution Analysis
          </h3>
          <div className="h-96">
            <SchemeDistributionChart chartType="schemes" />
          </div>
        </div>
      )}
      
      {selectedReport === 'demographic' && (
        <div className="space-y-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Age Distribution
            </h3>
            <div className="h-80">
              <SchemeDistributionChart chartType="age" />
            </div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Regional Distribution
            </h3>
            <div className="h-80">
              <SchemeDistributionChart chartType="region" />
            </div>
          </div>
        </div>
      )}
      
      {selectedReport === 'monthly' && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Monthly Progress Analysis
          </h3>
          <div className="h-96">
            <SchemeDistributionChart chartType="timeline" />
          </div>
        </div>
      )}
    </div>
  );
};

export default Reports;